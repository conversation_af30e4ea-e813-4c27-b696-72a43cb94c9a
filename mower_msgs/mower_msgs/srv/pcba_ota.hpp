#pragma once
#include <cstdint>
#include <iox/string.hpp>

namespace mower_msgs::srv {

struct PcbaOTARequest {
  iox::string<128> ota_image_path;
};

struct PcbaOTAResponse {
  bool success;
};

typedef enum {
  OTA_SUCCESS = 0,
  OTA_FAIL = 1,
} ota_result_t;

struct PcbaOTAResultRequest {
  uint8_t progress;
  ota_result_t ota_result;
};

struct PcbaOTAResultResponse {
  bool success;
};

}  // namespace mower_msgs::srv
