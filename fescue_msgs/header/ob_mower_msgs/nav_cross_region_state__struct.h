#pragma once

#include "geometry_msgs/pose__struct.h"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "nav_msgs/path__struct.h"
#include "std_msgs/header__struct.h"

#include <chrono>
#include <cstdint>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 跨区域状态
 */
typedef enum fescue_msgs_enum__CrossRegionState
{
    FESCUE_MSGS_ENUM_CROSS_REGION_EDGE_FINDING_BEACON = 0,                 // 新增沿边找信标状态
    FESCUE_MSGS_ENUM_CROSS_REGION_PER_FOUND_BEACON,                        // 感知定位已经找到信标
    FESCUE_MSGS_ENUM_CROSS_REGION_STAGE4_ENTERING_CHANNEL,                 /* 第四阶段，刚入通道，且已直行一定距离 */
    FESCUE_MSGS_ENUM_CROSS_REGION_STAGE4_LOC_NO_DETECT_BEACON,             /* 第四阶段，定位未检测到二维码 */
    FESCUE_MSGS_ENUM_CROSS_REGION_STAGE4_NON_GRASS_REACHED,                // 第四阶段，新增 NON_GRASS_REACHED 状态
    FESCUE_MSGS_ENUM_CROSS_REGION_STAGE4_LOC_DETECT_BEACON_NO_POS,         /* 第四阶段，定位检测到二维码，不能算出位姿 */
    FESCUE_MSGS_ENUM_CROSS_REGION_STAGE4_LOC_DETECT_BEACON_WITH_POS,       /* 第四阶段，定位检测到二维码，可以算出位姿 */
    FESCUE_MSGS_ENUM_CROSS_REGION_STAGE4_BEACON_EXIT_CROSS_REGION,         /* 第四阶段，信标检测退出跨区域 */
    FESCUE_MSGS_ENUM_CROSS_REGION_STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION, /* 第四阶段，非草地到草地退出跨区域 */

    FESCUE_MSGS_ENUM_CROSS_REGION_START,     /* 开始 */
    FESCUE_MSGS_ENUM_CROSS_REGION_FINISH,    /* 完成 */
    FESCUE_MSGS_ENUM_CROSS_REGION_UNDEFINED, /* 未定义 */
} fescue_msgs_enum__CrossRegionState;

/**
 * @brief 跨区域状态数据结构体
 */
typedef struct fescue_msgs__msg__CrossRegionStateData
{
    fescue_msgs_enum__CrossRegionState state;
} fescue_msgs__msg__CrossRegionStateData;

#ifdef __cplusplus
}
#endif
