#include "pure_pursuit_tracker.hpp"

#include "utils/logger.hpp"
#include "utils/time.hpp"

namespace fescue_iox
{

const char *TrackerResultString(const TrackerResult &result)
{
    switch (result)
    {
    case TrackerResult::kRunning:
        return "kRunning";
    case TrackerResult::kArrived:
        return "kArrived";
    case TrackerResult::kTimeout:
        return "kTimeout";
    case TrackerResult::kInvalid:
        return "kInvalid";
    default:
        return "Unknown";
    }
}

PurePursuitTracker::PurePursuitTracker(const std::vector<TrajectoryPose> &traj, const PurePursuitConfig &config)
    : traj_(traj)
    , config_(config)
    , linear_velocity_(0)
    , angular_velocity_(0)
    , result_(TrackerResult::kInvalid)
    , look_ahead_index_(-1)
    , min_dist_index_(-1)
{
    double length = CalculateLength(traj);
    is_spin_traj_ = IsSpinTraj(traj);
    if (is_spin_traj_)
    {
        timeout_time_ = config_.spin_timeout_time;
    }
    else
    {
        timeout_time_ = length / config_.min_linear * config_.timeout_time_ratio;
    }
    LOG_INFO("Timeout time: {} length: {} try_reach_goal_angle: {} is_spin_traj: {}",
             timeout_time_, length, config_.try_reach_goal_angle, is_spin_traj_);
}

void PurePursuitTracker::TrackMovingTraj(const TrajectoryPose &pose, const OdomResult &odom_result)
{
    (void)odom_result;
    UpdateCurrentIndex(pose);
    if (look_ahead_index_ < 0)
    {
        LOG_WARN("Look ahead index is less than 0");
        return;
    }
    double traj_sign = 1.0;
    for (const auto &pose : traj_)
    {
        if (pose.linear_velocity < -1e-6)
        {
            traj_sign = -1.0;
            break;
        }
    }
    const auto &look_ahead_pose = traj_[look_ahead_index_];
    const auto &min_dist_pose = traj_[min_dist_index_];
    // 根据到前瞻点的距离，计算线速度
    double look_ahead_dist_diff = std::hypot(look_ahead_pose.x - pose.x, look_ahead_pose.y - pose.y);
    double look_ahead_dist_ratio = config_.look_ahead_dist_ratio;
    linear_velocity_ = traj_sign * look_ahead_dist_ratio * look_ahead_dist_diff;
    // 限制线速度在最小值和最大值之间，注意符号
    double min_linear = config_.min_linear;
    double max_linear = config_.max_linear;
    double linear_sign = (linear_velocity_ > 0) ? 1.0 : -1.0;
    linear_velocity_ = std::max(min_linear, std::min(max_linear, std::abs(linear_velocity_)));
    linear_velocity_ = linear_sign * linear_velocity_;
    // 根据到最近点的距离和到前瞻点的角度差，计算角速度
    const auto &goal_pose = traj_[traj_.size() - 1];
    double goal_dist = std::hypot(goal_pose.x - pose.x, goal_pose.y - pose.y);
    double look_ahead_angle_diff = NormalizeAngle(look_ahead_pose.theta - pose.theta);
    double min_pose_dist = std::hypot(min_dist_pose.x - pose.x, min_dist_pose.y - pose.y);
    double cur_to_min_dist_angle = atan2(min_dist_pose.y - pose.y, min_dist_pose.x - pose.x);
    double cur_to_min_dist_angle_diff = NormalizeAngle(cur_to_min_dist_angle - pose.theta);
    double lat_dist_sign = (cur_to_min_dist_angle_diff > 1e-6) ? 1.0 : -1.0;
    double angle_diff_ratio = config_.angle_diff_ratio;
    double lat_dist_ratio = config_.lat_dist_ratio;
    if (config_.try_reach_goal_angle && goal_dist < config_.near_goal_dist)
    {
        lat_dist_ratio = config_.small_lat_dist_ratio;
        angle_diff_ratio = config_.big_angle_diff_ratio;
    }
    angular_velocity_ = angle_diff_ratio * look_ahead_angle_diff + traj_sign * lat_dist_ratio * lat_dist_sign * min_pose_dist;
    // 限制角速度在最小值和最大值之间，注意符号
    double min_angular = config_.min_angular;
    double max_angular = config_.max_angular;
    double angular_sign = (angular_velocity_ > 0) ? 1.0 : -1.0;
    angular_velocity_ = std::max(min_angular, std::min(max_angular, std::abs(angular_velocity_)));
    angular_velocity_ = angular_sign * angular_velocity_;
    LOG_INFO_THROTTLE(500, "Linear velocity: {} Angular velocity: {} look_ahead_index: {} min_dist_index: {} traj_sign: {} look_ahead_dist_diff: {} look_ahead_angle_diff: {} min_pose_dist: {} lat_dist_sign: {} goal_dist: {}",
                      linear_velocity_, angular_velocity_, look_ahead_index_, min_dist_index_, traj_sign, look_ahead_dist_diff, look_ahead_angle_diff, min_pose_dist, lat_dist_sign, goal_dist);
    // 判达
    if (last_dist_to_goal_ > -1e-6)
    {
        if (goal_dist < config_.arrived_dist && goal_dist > last_dist_to_goal_)
        {
            LOG_INFO("arrived goal_dist: {} last_dist_to_goal_: {} cur pose: x={} y={} theta={} goal x={} y={} theta={}",
                     goal_dist, last_dist_to_goal_, pose.x, pose.y, pose.theta, goal_pose.x, goal_pose.y, goal_pose.theta);
            result_ = TrackerResult::kArrived;
        }
    }
    last_dist_to_goal_ = goal_dist;
}

void PurePursuitTracker::Update(const TrajectoryPose &pose, const OdomResult &odom_result)
{
    if (traj_.empty())
    {
        LOG_INFO("Trajectory is empty");
        return;
    }
    if (result_ == TrackerResult::kArrived || result_ == TrackerResult::kTimeout)
    {
        return;
    }
    auto time_now = GetSteadyClockTimestampMs();

    if (is_spin_traj_)
    {
        TrackSpinTraj(pose, odom_result);
    }
    else
    {
        TrackMovingTraj(pose, odom_result);
    }

    if (start_time_ == 0)
    {
        start_time_ = time_now;
    }
    if (time_now - start_time_ > static_cast<uint64_t>(timeout_time_ * 1000))
    {
        LOG_INFO("Timeout time: {} start_time: {} time_now: {}", timeout_time_, start_time_, time_now);
        result_ = TrackerResult::kTimeout;
    }
}

void PurePursuitTracker::TrackSpinTraj(const TrajectoryPose &pose, const OdomResult &odom_result)
{
    (void)odom_result;
    if (traj_.size() != 1)
    {
        LOG_WARN("Spin trajectory size is not 1");
        return;
    }
    const auto &spin_end_pose = traj_[0];
    double angle_diff = NormalizeAngle(spin_end_pose.theta - pose.theta);
    double angular_velocity = config_.spin_angular_ratio * angle_diff;
    double min_angular = config_.spin_min_angular;
    double max_angular = config_.spin_max_angular;
    double angular_sign = (angular_velocity > 0) ? 1.0 : -1.0;
    angular_velocity = std::max(min_angular, std::min(max_angular, std::abs(angular_velocity)));
    angular_velocity = angular_sign * angular_velocity;
    linear_velocity_ = 0;
    angular_velocity_ = angular_velocity;
    bool is_over_arrived = false;
    if (angle_diff > 0 && last_angle_diff_ < 0)
    {
        is_over_arrived = true;
    }
    else if (angle_diff < 0 && last_angle_diff_ > 0)
    {
        is_over_arrived = true;
    }
    LOG_INFO_THROTTLE(1000, "spin traj angular velocity: {} linear_velocity_: {} end pose theta: {} cur pose theta: {} angle_diff: {} angular_sign: {} is_over_arrived: {}",
                      angular_velocity, linear_velocity_, spin_end_pose.theta, pose.theta, angle_diff, angular_sign, is_over_arrived);
    if (abs(angle_diff) < config_.small_spin_arrived_angle)
    {
        LOG_INFO("arrived small spin traj angle_diff: {} last_angle_diff_: {}", angle_diff, last_angle_diff_);
        result_ = TrackerResult::kArrived;
    }
    else if (abs(angle_diff) < config_.spin_arrived_angle && is_over_arrived)
    {
        LOG_INFO("arrived spin traj angle_diff: {} last_angle_diff_: {}", angle_diff, last_angle_diff_);
        result_ = TrackerResult::kArrived;
    }
    last_angle_diff_ = angle_diff;
}

void PurePursuitTracker::UpdateCurrentIndex(const TrajectoryPose &pose)
{
    // 找到最近点
    int min_dist_index = 0;
    int look_ahead_index = 0;

    int index = std::max(0, min_dist_index_);
    double min_dist = std::numeric_limits<double>::max();
    for (size_t i = index; i < traj_.size(); ++i)
    {
        const auto &traj_pose = traj_[i];
        double dist = std::hypot(traj_pose.x - pose.x, traj_pose.y - pose.y);
        if (dist < min_dist)
        {
            min_dist = dist;
            min_dist_index = i;
        }
    }
    min_dist_index_ = min_dist_index;
    const auto &min_dist_pose = traj_[min_dist_index];
    LOG_INFO_THROTTLE(1000, "Min dist pose min_dist_index={} look_ahead_index_={} x={} y={} theta={} cur pose: x={} y={} theta={}",
                      min_dist_index, look_ahead_index_, min_dist_pose.x, min_dist_pose.y, min_dist_pose.theta, pose.x, pose.y, pose.theta);
    look_ahead_index = min_dist_index;
    // 找到前瞻点
    double look_ahead_dist = config_.look_ahead_dist;
    double dist = 0;
    while ((size_t)look_ahead_index < traj_.size())
    {
        const auto &look_ahead_pose = traj_[look_ahead_index];
        TrajectoryPose last_pose;
        if (look_ahead_index > 0)
        {
            last_pose = traj_[look_ahead_index - 1];
        }
        else
        {
            last_pose = look_ahead_pose;
        }
        dist += std::hypot(look_ahead_pose.x - last_pose.x, look_ahead_pose.y - last_pose.y);
        if (dist > look_ahead_dist)
        {
            break;
        }
        look_ahead_index = look_ahead_index + 1;
    }
    if ((size_t)look_ahead_index >= traj_.size())
    {
        look_ahead_index = traj_.size() - 1;
    }
    if (look_ahead_index > look_ahead_index_)
    {
        LOG_INFO("Look ahead index: {} x={} y={} theta={} cur pose x={} y={} theta={}",
                 look_ahead_index, traj_[look_ahead_index].x, traj_[look_ahead_index].y, traj_[look_ahead_index].theta, pose.x, pose.y, pose.theta);
        look_ahead_index_ = look_ahead_index;
    }
}

bool PurePursuitTracker::IsSpinTraj(const std::vector<TrajectoryPose> &traj) const
{
    if (traj.size() != 1)
    {
        return false;
    }
    const auto &pose = traj[0];
    if (std::abs(pose.linear_velocity) < 1e-6 && std::abs(pose.angular_velocity) > 1e-6)
    {
        return true;
    }
    return false;
}

double PurePursuitTracker::CalculateLength(const std::vector<TrajectoryPose> &traj) const
{
    double length = 0;
    for (size_t i = 1; i < traj.size(); ++i)
    {
        length += std::hypot(traj[i].x - traj[i - 1].x, traj[i].y - traj[i - 1].y);
    }
    return length;
}

} // namespace fescue_iox