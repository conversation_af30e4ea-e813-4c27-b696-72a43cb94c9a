#include "recharge.hpp"

#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "recharge_config.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <numeric>
#include <string>
#include <utility>
#include <vector>

using namespace mower_msgs::msg;

namespace fescue_iox
{

NavigationRechargeAlg::NavigationRechargeAlg(const RechargeAlgParam &param)
    : vel_publisher_(std::make_unique<VelocityPublisher>("Recharge"))
{
    InitPublisher();
    SetRechargeAlgParam(param);
}

NavigationRechargeAlg::~NavigationRechargeAlg()
{
    PublishZeroVelocity();
    LOG_WARN("NavigationRechargeAlg exit!");
}

void NavigationRechargeAlg::SetAlgoRunningState(MowerRunningState state)
{
    LOG_INFO("NavigationRechargeAlg running state: {}", static_cast<int>(state));
    mower_running_state_ = state;
    if (state == MowerRunningState::RUNNING)
    {
        ResumeVelocity();
    }
    else if (state == MowerRunningState::PAUSE)
    {
        PauseVelocity();
    }
    else
    {
        LOG_ERROR("[NavigationRechargeAlg] Unknown state {}!", static_cast<int>(state));
    }
}

void NavigationRechargeAlg::HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                                        std::chrono::seconds &perception_drive_duration, int &perception_drive_cooldown_time_threshold) // 感知检测结果
{
    if (perception_drive_duration.count() >= perception_drive_cooldown_time_threshold)
    {
        is_cooldown_active = false;
        is_in_cooldown_time_ = true;
        LOG_DEBUG("[BeaconRechargeDetection] 计时时间超过({})秒，冷却结束", perception_drive_cooldown_time_threshold);
        LOG_DEBUG("[BeaconRechargeDetection] 开启感知驱动，同时关闭沿边");
        thread_control_ = ThreadControl::UNDEFINED;
        UpdateFeatureSelection(thread_control_);
        PerceptionBasedAdjustment(mark_loc_result);
    }
    else
    {
        // 冷却未结束，跳过执行
        LOG_DEBUG("[BeaconRechargeDetection] 冷却时间未结束，未超过({})秒", perception_drive_cooldown_time_threshold);

        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
        LOG_DEBUG("[BeaconRechargeDetection] 开启沿边");
    }
}

void NavigationRechargeAlg::HandleEdgePerceptionBeaconRechargeDetection(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active)
{
    if (is_cooldown_active) // 冷却机制激活
    {
        LOG_DEBUG("[BeaconRechargeDetection] 冷却机制激活");

        auto current_time = std::chrono::steady_clock::now();
        edge_perception_drive_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_cooldown_time_);

        // 打印时间差
        LOG_DEBUG("[BeaconRechargeDetection] 沿边感知驱动冷却的计时（秒）：({})", edge_perception_drive_duration_.count());
        HandleEdgeCooldownMechanism(mark_loc_result, is_cooldown_active, edge_perception_drive_duration_, edge_perception_drive_cooldown_time_threshold_);
    }
    else // 冷却机制未激活
    {
        LOG_DEBUG("[BeaconRechargeDetection] 冷却机制未激活");
        LOG_DEBUG("[BeaconRechargeDetection] 开启感知驱动，同时关闭沿边");
        is_in_cooldown_time_ = true;
        thread_control_ = ThreadControl::UNDEFINED;
        UpdateFeatureSelection(thread_control_);
        PerceptionBasedAdjustment(mark_loc_result);
    }
}

void NavigationRechargeAlg::PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result)
{
    switch (mark_loc_result.mark_perception_direction)
    {
    case -1: // 偏左，左转
        PublishVelocity(0, mower_angular_);
        break;

    case 0: // 居中，直线
        PublishVelocity(mower_linear_, 0);
        break;

    case 1: // 偏右，右转
        PublishVelocity(0, -mower_angular_);
        break;

    default:
        PublishVelocity(mower_linear_, 0);
        break;
    }
}

void NavigationRechargeAlg::FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx)
{
    for (size_t i = 0; i < mark_id_distance_vec.size(); ++i)
    {
        if (mark_id_distance_vec[i].distance < mark_distance_threshold_) // 50cm
        {
            shortest_dis_inx = i;
        }
    }
}

/**
 * @brief 持续检测信标状态
 *
 * @param mark_loc_result
 * @param qrcode_loc_result
 * @param is_beacon_valid
 */
void NavigationRechargeAlg::ProcessBeaconRechargeDetection(const MarkLocationResult &mark_loc_result, bool &is_beacon_valid)
{
    if (mark_loc_result.mark_perception_status == 0) // 感知没有检测到信标
    {
        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
    else // 感知检测到信标
    {
        LOG_DEBUG("[BeaconRechargeDetection] 2. 感知检测到信标");

        if (mark_loc_result.mark_id_distance.size() <= 0) // 定位的mark_id_distance没值
        {
            LOG_DEBUG("[BeaconRechargeDetection] 2.1 定位的mark_id_distance无值");
            HandleEdgePerceptionBeaconRechargeDetection(mark_loc_result, is_cooldown_active_);
        }
        else // 定位的mark_id_distance有值
        {
            LOG_DEBUG("[BeaconRechargeDetection] 2.2 定位的mark_id_distance有值");

            // 判断信标是否有效。（mark_id_distance小于50cm认为当前信标有效）
            int shortest_dis_inx = -1;
            std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
            FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);

            if (shortest_dis_inx == -1) // 若信标无效。不操作，继续之前的动作
            {
                LOG_DEBUG("[BeaconRechargeDetection] 2.2.1 跨区信标无效");
                HandleEdgePerceptionBeaconRechargeDetection(mark_loc_result, is_cooldown_active_);
            }
            else // 若信标有效。检测栈容器是否为空
            {
                LOG_DEBUG("[BeaconRechargeDetection] 2.2.2 跨区信标有效");
                LOG_DEBUG("[BeaconRechargeDetection] 2.2.2 有效的信标为 mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                current_mark_id_ = mark_id_distance_vec[shortest_dis_inx].mark_id;
                if (first_detection_beacon_)
                {
                    beacon_status_.mark_id = current_mark_id_;
                    beacon_status_.beacon_look_count = 1;
                    first_detection_beacon_ = false;
                }
                is_beacon_valid = true;
            }
        }
    }
}

void NavigationRechargeAlg::SetCrossRegionRunningStateCallback(std::function<void(CrossRegionRunningState)> callback)
{
    cross_region_running_state_callback_ = callback;
}

void NavigationRechargeAlg::UpdateCrossRegionRunningState(CrossRegionRunningState state)
{
    if (cross_region_running_state_callback_)
    {
        cross_region_running_state_callback_(state);
    }
}
void NavigationRechargeAlg::ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular)
{
    float sign = UnifyAngle(yaw_des - yaw_first) >= 0.0 ? 1.0 : -1.0; // 默认旋转方向 1.0左转 -1.0右转

    float ang_err = fabsf(UnifyAngle(yaw_des - yaw_first));
    uint64_t t = (ang_err / vel_angular) * 1000; // 转弯持续时间 ms
    LOG_DEBUG("[BeaconRechargeDetection][ExitPile]旋转角度 = {}", Radians2Degrees(ang_err));
    LOG_DEBUG("[BeaconRechargeDetection][ExitPile]角速度 = {}, 时间 = {}", sign * vel_angular, ang_err / vel_angular);
    PublishVelocity(0, sign * vel_angular, t);
}

void NavigationRechargeAlg::ControlLinearMotion(const float &pass_point, const float &location,
                                                const float &vel_linear, const int &reverse)
{
    float dis = fabsf(pass_point - location);
    uint64_t t = (dis / vel_linear) * 1000;
    LOG_DEBUG("[BeaconRechargeDetection] [ExitPile] 直行距离 dis = {}", dis);
    LOG_DEBUG("[BeaconRechargeDetection] [ExitPile] 直行速度 = {}, 时间 = {}", reverse * vel_linear, dis / vel_linear);
    PublishVelocity(reverse * vel_linear, 0, t);
}

bool NavigationRechargeAlg::HandleRechargeCrossRegionStates(const CrossRegionRunningState &cross_region_state)
{
    bool cross_region_to_recharge = false;
    if ((cross_region_state == CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION) ||
        (cross_region_state == CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION) ||
        (cross_region_state == CrossRegionRunningState::FINISH))
    {
        LOG_WARN("[BeaconRechargeDetection] 关闭跨区域, 切换到回充");
        if (edge_mode_direction_ == 1) // 沿边顺时针
        {
            LOG_DEBUG("[CrossRegion] 不考虑顺时针沿边跨区域的情况");
        }
        else // 沿边逆时针
        {
            LOG_DEBUG("[BeaconRechargeDetection] 沿边逆时针的情况下。向右转一定角度");
            ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_); // 右转
        }
        CrossRegionDisable();
        cross_region_to_recharge = true;
    }
    return cross_region_to_recharge;
}

/**
 * @brief 对正整数进行配对
 *
 * 规则说明：
 * 如果输入的正整数 n 为奇数，则返回 n+1；
 * 如果 n 为偶数，则返回 n-1。
 * 公式表示如下：
 * $$\text{若 } n \% 2 == 1, \text{ 则输出 } n+1$$
 * $$\text{若 } n \% 2 == 0, \text{ 则输出 } n-1$$
 *
 * @param n 正整数
 * @return int 配对后的数值
 */
int NavigationRechargeAlg::PairNumber(int n)
{
    // 判断 n 是否为奇数
    if (n % 2 == 1)
    {
        // 如果 n 为奇数，返回 n+1
        return n + 1;
    }
    else
    {
        // 如果 n 为偶数，返回 n-1
        return n - 1;
    }
}

/**
 * @brief 用于回充过程中进行安规状态的发出
 */
void NavigationRechargeAlg::SendRechargeRunningState()
{
    if (qr_code_direction_is_ok_ && qr_code_y_is_ok_)
    {
        UpdateRechargeRunningState(RechargeRunningState::PER_FOUND_QR_CODE); // 感知定位已经找到充电桩二维码 /**不允许切换状态 */
    }
    else if (is_qr_code_detected_)
    {
        UpdateRechargeRunningState(RechargeRunningState::PROCESS_FOUND_QR_CODE);
    }
    else if (cross_region_flag_)
    {
        UpdateRechargeRunningState(RechargeRunningState::PROCESS_CROSSREGION);
    }
    else
    {
        UpdateRechargeRunningState(RechargeRunningState::EDGE_FINDING_QR_CODE); // 新增沿边找充电桩二维码 /**该状态允许切换状态 */
    }
}

/**
 * @brief 用于回充过程中进行跨区二维码的判断
 */
bool NavigationRechargeAlg::ProcessBeaconRechargeDetection(const MarkLocationResult &mark_loc_result)
{
    bool is_beacon_valid = false; // 默认为信标无效
    if (!(single_region_recharge_flag_ || cross_to_recharge_ || cross_region_flag_))
    {
        ProcessBeaconRechargeDetection(mark_loc_result, is_beacon_valid);
    }
    return is_beacon_valid;
}

/**
 * @brief 用于回充过程中进行跨区状态的监听
 */
void NavigationRechargeAlg::CrossRegionBeaconAdjust(const CrossRegionRunningState &cross_region_state)
{
    if (cross_region_flag_)
    {
        LOG_DEBUG("Start Cross Region");
        cross_to_recharge_ = HandleRechargeCrossRegionStates(cross_region_state);
        if (cross_to_recharge_)
        {
            LOG_WARN("Ending Cross Region");
            cross_region_flag_ = false;
        }
    }
}

/**
 * @brief 单区域回充状态判断
 */
void NavigationRechargeAlg::SingleAreaStateAdjust(const ChargeStationDetectResult &station_result,
                                                  const QRCodeLocationResult &qrcode_result)
{
    if (qrcode_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE)
    {
        if ((sqrt(pow(qrcode_result.xyzrpw.x, 2) + pow(qrcode_result.xyzrpw.y, 2)) < start_recharge_distance_) &&
            (abs(station_result.head_center_error) < head_center_max_dist_) &&
            fabs(qrcode_result.xyzrpw.w) < qr_code_clear_angle_)
        {
            single_region_recharge_flag_ = true;
            LOG_DEBUG("Recharge Single Region Start");
        }
    }
}

/**
 * @brief 跨区二维码状态判断,两次有效即认为可以跨区
 * @param is_beacon_valid 跨区二维码状态检测标志
 */
void NavigationRechargeAlg::MultiAreaStateAdjust(bool &is_beacon_valid)
{
    if (!cross_region_flag_)
    {
        if (is_beacon_valid)
        {
            LOG_INFO_THROTTLE(1000, "[BeaconRechargeDetection] 开始回充探索二维码探索模式");

            // 重置冷却时间戳，并激活冷却机制
            last_cooldown_time_ = std::chrono::steady_clock::now();
            is_cooldown_active_ = true;

            LOG_INFO("[BeaconRechargeDetection] 回充检测到信标二维码位姿有效，当前检测次数: {}", beacon_status_.beacon_look_count);
            LOG_INFO("[BeaconRechargeDetection] 回充当前检测mark_id: {}", current_mark_id_);
            // 只在首次进入时初始化
            if (is_first_enter_last_mark_detection_time_)
            {
                last_mark_detection_time_ = std::chrono::steady_clock::now();
                is_first_enter_last_mark_detection_time_ = false;
                LOG_WARN("[BeaconRechargeDetection] 回充信标检测开始计时");
            }
            auto current_time = std::chrono::steady_clock::now();
            mark_detection_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_mark_detection_time_);

            auto current_time_sec = std::chrono::duration_cast<std::chrono::seconds>(current_time.time_since_epoch());
            auto last_mark_detection_time_sec = std::chrono::duration_cast<std::chrono::seconds>(last_mark_detection_time_.time_since_epoch());
            LOG_WARN("[BeaconRechargeDetection]  回充当前时间戳（秒）current_time_sec({})", current_time_sec.count());
            LOG_WARN("[BeaconRechargeDetection] 回充上一时间戳（秒）last_mark_detection_time_sec ({})", last_mark_detection_time_sec.count());
            LOG_WARN("[BeaconRechargeDetection] 回充信标检测冷却的计时（秒）：({})", mark_detection_duration_.count());
            if (mark_detection_duration_.count() > mark_detection_cooldown_time_threshold_)
            {
                if (current_mark_id_ == beacon_status_.mark_id) // 同一个mark_id
                {
                    beacon_status_.beacon_look_count++;
                    last_mark_detection_time_ = std::chrono::steady_clock::now();
                    LOG_WARN("[BeaconRechargeDetection] 回充检测到信标二维码位姿有效，当前检测次数: {}", beacon_status_.beacon_look_count);
                    LOG_WARN("[BeaconRechargeDetection] 回充同一个mark_id, 当前检测mark_id: {}", current_mark_id_);
                }
                else // 不同mark_id
                {
                    beacon_status_ = BeaconStatus(current_mark_id_, 1);
                    last_mark_detection_time_ = std::chrono::steady_clock::now();
                    LOG_WARN("[BeaconRechargeDetection] 回充检测到信标二维码位姿有效，当前检测次数: {}", beacon_status_.beacon_look_count);
                    LOG_WARN("[BeaconRechargeDetection] 回充不同mark_id, 当前检测mark_id: {}", current_mark_id_);
                    LOG_WARN("[BeaconRechargeDetection] 回充不同mark_id, 上一检测mark_id: {}", beacon_status_.mark_id);
                }
            }

            if (beacon_status_.beacon_look_count >= 2)
            {
                // 启动跨区域进程
                LOG_INFO("[BeaconRechargeDetection] 信标检测超过两次，启动跨区域进程");
                // 切换到跨区域线程，并更新功能选择
                thread_control_ = ThreadControl::CROSS_REGION_THREAD;
                UpdateFeatureSelection(thread_control_);
                EdgeFollowDisable();
                cross_region_flag_ = true;
                // 重置状态
                next_paired_beacon_id_ = PairNumber(current_mark_id_);
                beacon_status_ = BeaconStatus(next_paired_beacon_id_, 1);
                LOG_INFO("[BeaconRechargeDetection]下一对信标id为 {}", next_paired_beacon_id_);
            }
            else
            {
                // 继续沿边
                LOG_INFO_THROTTLE(2000, "[BeaconRechargeDetection] 信标检测未超过两次，继续沿边");
                thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
                UpdateFeatureSelection(thread_control_);
                CrossRegionDisable();
            }
        }
    }
}

/**
 * @brief 上桩过程中二维码突然消失或者偏差过大
 */
void NavigationRechargeAlg::ProcessNoQRCodeInDetectProcess(const ChargeStationDetectResult &station_result)
{
    if (qr_code_direction_is_ok_ && qr_code_y_is_ok_ && (!qr_code_x_is_ok_) && (!charge_terminal_status_))
    {
        if (station_result.is_head)
        {
            charge_station_head_data_.push_back(1);
        }
        else
        {
            charge_station_head_data_.push_back(0);
        }

        if ((int)charge_station_head_data_.size() > save_head_detect_num_)
        {
            charge_station_head_data_.erase(charge_station_head_data_.begin());

            bool charge_head_flag = false;

            for (size_t i = 0; i < charge_station_head_data_.size(); i++)
            {
                if (charge_station_head_data_[i] == 1)
                {
                    charge_head_flag = true;
                    break;
                }
            }
            if (!charge_head_flag)
            {
                LOG_INFO("Charge Station No QR Code And Process Rotate!!!");
                rotate_recharge_flag_ = true;
                charge_station_head_data_.clear();
            }
            else
            {
                if (station_result.head_center_error > head_center_max_dist_)
                {
                    LOG_INFO("Max Head Station Error And Back!!!");
                    ProcessDock();
                    charge_station_head_data_.clear();
                }
            }
        }
    }
}

void NavigationRechargeAlg::ResetOtherStates()
{
    no_qr_num_ = 0;
    EdgeFollowDisable();  // 停止沿边
    RandomMowerDisable(); // 停止随机割草
    CrossRegionDisable();
}

RechargeAlgResult NavigationRechargeAlg::DoRecharge(const MarkLocationResult &mark_loc_result,
                                                    const CrossRegionRunningState &cross_region_state,
                                                    const ChargeStationDetectResult &station_result,
                                                    const QRCodeLocationResult &qrcode_result,
                                                    McuExceptionStatus &mcu_exception_status)
{
    if (mower_running_state_ == MowerRunningState::PAUSE)
    {
        LOG_WARN_THROTTLE(3000, "Recharge DoRecharge() is PAUSE!");
        if (qr_code_direction_is_ok_ && qr_code_y_is_ok_)
        {
            straight_time_ = 0;
            straight_begin_time_ = 0;
        }
        return RechargeAlgResult(false, true);
    }

    SendRechargeRunningState();

    if (perception_recharge_flag_)
    {
        ResetOtherStates();
        if (pre_station_timestamp_ms_ != station_result.timestamp_ms)
        {
            LOG_WARN("In Perception Recharge Process, timestamp_ms:{}, station_result:{}.", station_result.timestamp_ms, station_result.is_head);
            pre_station_timestamp_ms_ = station_result.timestamp_ms;
            if (ProcessNoQRCodeAndPerceptionRecharge(station_result, mcu_exception_status))
            {
                return RechargeAlgResult(true, dock_success_);
            }
            else
            {
                return RechargeAlgResult(false, dock_success_);
            }
        }
        else
        {
            return RechargeAlgResult(false, dock_success_);
        }
    }

    if (rotate_recharge_flag_)
    {
        ResetOtherStates();
        if ((pre_station_timestamp_ms_ != station_result.timestamp_ms) && (pre_qr_timestamp_ms_ != qrcode_result.timestamp_ms))
        {
            LOG_WARN("In Rotate Recharge Process, station_result.timestamp_ms:{}, qrcode_result.timestamp_ms:{}.", station_result.timestamp_ms, qrcode_result.timestamp_ms);
            pre_station_timestamp_ms_ = station_result.timestamp_ms;
            pre_qr_timestamp_ms_ = qrcode_result.timestamp_ms;
            if (ProcessNoQRCodeAndRotateRecharge(station_result, qrcode_result, mcu_exception_status))
            {
                LOG_WARN("We Find Recharge Station Again And Stop Rotate");
                ResetRechargeFlags(false);
                UpdateRechargeRunningState(RechargeRunningState::UNDEFINED); // 未定义 /**该状态允许切换状态
                is_move_to_station_complete_ = true;
            }
            else
            {
                return RechargeAlgResult(false, dock_success_);
            }
        }
        else
        {
            return RechargeAlgResult(false, dock_success_);
        }
    }

    if (pre_abnormal_timestamp_ms_ != station_result.timestamp_ms)
    {
        pre_abnormal_timestamp_ms_ = station_result.timestamp_ms;
        ProcessNoQRCodeInDetectProcess(station_result);
    }

    bool is_beacon_valid = ProcessBeaconRechargeDetection(mark_loc_result);

    SingleAreaStateAdjust(station_result, qrcode_result);

    CrossRegionBeaconAdjust(cross_region_state);

    if (single_region_recharge_flag_ || cross_to_recharge_)
    {
        single_region_recharge_flag_ = true;
        ResetCrossRegionFlags();
        ResetCrossRegionBeaconStatus();
        // 如果原地旋转完成但尚未移动到充电桩
        if (!is_move_to_station_complete_)
        {
            // 如果二维码检测有结果了
            if (station_result.is_head && (qrcode_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE))
            {
                // 如果二维码检测有结果了，且距离小于阈值，则认为靠近充电桩了
                if ((sqrt(pow(qrcode_result.xyzrpw.x, 2) + pow(qrcode_result.xyzrpw.y, 2)) < start_recharge_distance_) &&
                    (abs(station_result.head_center_error) < head_center_max_dist_) &&
                    fabs(qrcode_result.xyzrpw.w) < qr_code_clear_angle_)
                {
                    ResetOtherStates();
                    LOG_WARN("detect qr code, skip the move to station stage!"); // 打印警告信息，检测到二维码，跳过移动到充电桩阶段
                    is_move_to_station_complete_ = true;
                    is_qr_code_detected_ = true; // 二维码检测到
                    adjust_num_ = 0;
                }
                else
                {
                    ProcessNoQRCodeAndEdgeFollow();
                }
            }
            else
            {
                ProcessNoQRCodeAndEdgeFollow();
            }
        }
    }
    else
    {
        MultiAreaStateAdjust(is_beacon_valid);
    }

    if (!head_direction_flag_ && !(qr_code_direction_is_ok_ && qr_code_y_is_ok_))
    {
        if (detect_time_ == 0 && detect_begin_time_ == 0)
        {
            detect_time_ = detect_stay_time_; // 前进持续时间 ms
            detect_begin_time_ = GetTimestampMs();
        }
        uint64_t detect_run_time = GetTimestampMs() - detect_begin_time_;
        LOG_WARN_THROTTLE(1500, "Deteck Time, run time {} detect time {}!", detect_run_time, detect_time_);
        if (detect_run_time < detect_time_)
        {
            if (ProcessRechargeRotate(station_result, qrcode_result))
            {
                detect_time_ = 0;
                detect_begin_time_ = 0;
                head_direction_flag_ = true;
            }
        }
        else
        {
            // ProcessNoQRCodeAndEdgeFollow();
            LOG_WARN("In Detect Process, No QR Code And Running Rotate Recharge");
            rotate_recharge_flag_ = true;
            ResetOtherStates();
            return RechargeAlgResult(false, dock_success_);
        }
    }

    // 已经靠近充电桩了，判断二维码识别结果
    if (head_direction_flag_ && is_move_to_station_complete_ && !is_qr_code_complete_)
    {
        if (ProcessQRCodeDetect(station_result, qrcode_result, mcu_exception_status)) // 处理二维码识别结果并检查是否完成
        {
            LOG_WARN("is_qr_code_complete_ true");
            is_qr_code_complete_ = true; // 如果二维码识别完成，标记为完成状态
        }
    }

    return RechargeAlgResult(is_qr_code_complete_, dock_success_);
}

void NavigationRechargeAlg::SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback)
{
    feature_select_callback_ = callback;
}

void NavigationRechargeAlg::UpdateFeatureSelection(const ThreadControl &thread_control)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData edge_follow{ThreadControl::PERCEPTION_EDGE_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData cross_region{ThreadControl::CROSS_REGION_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData random_mower{ThreadControl::RANDOM_MOWING_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData behavior{ThreadControl::BEHAVIOR_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData spiral_mower{ThreadControl::SPIRAL_MOWING_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData escape{ThreadControl::ESCAPE_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};

    switch (thread_control)
    {
    case ThreadControl::PERCEPTION_EDGE_THREAD:                               // 沿边：可以有跨区域和回充
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. 随机割草
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);   // 2. 沿边
        // cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. 恢复
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. 螺旋割草
        escape.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);        // 7. 脱困
        break;

    case ThreadControl::CROSS_REGION_THREAD:                                  // 跨区域：可以有沿边
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. 随机割草
        // edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);   // 2. 沿边
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE); // 3. 跨区域

        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. 恢复
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. 螺旋割草
        break;

    case ThreadControl::RANDOM_MOWING_THREAD:                                 // 随机割草：
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 1. 随机割草
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. 沿边
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. 恢复
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. 螺旋割草
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. 脱困
        break;

    case ThreadControl::BEHAVIOR_THREAD:                                      // 恢复行为
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. 随机割草
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. 沿边
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);      // 5. 恢复
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. 螺旋割草
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. 脱困
        break;

    case ThreadControl::SPIRAL_MOWING_THREAD:                                 // 螺旋割草
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. 随机割草
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. 沿边
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. 恢复
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 6. 螺旋割草
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. 脱困
        break;

    case ThreadControl::CLOSE_ALL_TASK:
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. 随机割草
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. 沿边
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. 恢复
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. 螺旋割草
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. 脱困
        break;

    case ThreadControl::UNDEFINED:
        // random_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 1. 随机割草
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. 沿边
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. 脱困
        break;

    default:
        // random_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 1. 随机割草
        // edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. 沿边
        // cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        // rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. 回充
        // behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 5. 恢复
        break;
    }

    feature_data.push_back(random_mower);
    feature_data.push_back(edge_follow);
    feature_data.push_back(cross_region);
    feature_data.push_back(behavior);
    feature_data.push_back(spiral_mower);
    feature_data.push_back(escape);

    if (feature_select_callback_)
    {
        feature_select_callback_(feature_data);
    }
}

void NavigationRechargeAlg::SetRechargeAlgParam(const RechargeAlgParam &param)
{
    try_max_num_ = param.try_max_num;     // 重复探索最大次数
    no_qr_max_num_ = param.no_qr_max_num; // 记录得不到QR码最大次数
    save_data_num_ = param.save_data_num; // 保存数据帧数
    save_terminal_num_ = param.save_terminal_num;
    head_center_min_dist_ = param.head_center_min_dist;                   // 充电桩头像素偏差
    head_center_max_dist_ = param.head_center_max_dist;                   // 回充充电桩可调像素距离阈值
    station_qr_direction_min_dist_ = param.station_qr_direction_min_dist; // 二维码检测中心和充电桩检测中心相对方位最小像素阈值

    qr_code_clear_angle_ = param.qr_code_clear_angle;         // 1.13(65度)
    qr_code_detect_angle_1_ = param.qr_code_detect_angle_1;   // 0.2618(15度), 0.1745(10度)，0.0873(5度)
    qr_code_min_distance_ = param.qr_code_min_distance;       // 离二维码最近距离 0.65 m
    start_recharge_distance_ = param.start_recharge_distance; // 开始回充距离 1.5 m
    qr_code_x_min_dist_ = param.qr_code_x_min_dist;           // 调整y和yaw时x的最大距离阈值 0.92 m
    qr_code_y_min_dist_ = param.qr_code_y_min_dist;
    circle_r_dist_ = param.circle_r_dist; // 转弯半径最大距离阈值 1.2 m
    explore_distance_ = param.explore_distance;
    explore_vel_ = param.explore_vel;
    kp_y_ = param.kp_y;
    kp_yaw_ = param.kp_yaw;
    kp_perception_ = param.kp_perception;
    recharge_adjust_linear_ = param.recharge_adjust_linear;
    recharge_pile_linear_ = param.recharge_pile_linear;
    recharge_pile_angular_ = param.recharge_pile_angular;
}

void NavigationRechargeAlg::ResetRechargeFlags(bool dock_status)
{
    if (dock_status)
    {
        dock_num_++;
        PublishVelocity(-recharge_pile_linear_, 0, backward_time_);
    }
    else
    {
        dock_num_ = 0;
        dock_success_ = true;
    }
    straight_time_ = 0;
    no_qr_num_ = 0;
    straight_begin_time_ = 0;
    perception_straight_time_ = 0;
    perception_straight_begin_time_ = 0;
    rotate_time_ = 0; // 前进持续时间 ms
    rotate_begin_time_ = 0;
    save_qr_x_tmp_.clear();
    save_qr_y_tmp_.clear();
    save_qr_w_tmp_.clear();
    detect_time_ = 0;
    detect_begin_time_ = 0;
    adjust_num_ = 0;
    is_move_to_station_complete_ = false;
    is_qr_code_complete_ = false;
    qr_code_direction_is_ok_ = false;
    qr_code_y_is_ok_ = false;
    qr_code_x_is_ok_ = false;
    collision_occur_ = false;
    head_direction_flag_ = true;
    record_stable_x_data_ = 0;
    is_qr_code_detected_ = false;
    pre_timestamp_ms_ = 0;
    perception_recharge_flag_ = false;
    rotate_recharge_flag_ = false;
}

void NavigationRechargeAlg::ResetCrossRegionFlags()
{
    cross_to_recharge_ = false;
    cross_region_flag_ = false;
}

void NavigationRechargeAlg::ResetCrossRegionBeaconStatus()
{
    last_mark_detection_time_ = std::chrono::steady_clock::now();

    // 新增
    is_cooldown_active_ = false;
    is_in_cooldown_time_ = false;
    first_detection_beacon_ = true;
    is_first_enter_last_mark_detection_time_ = true;
    last_cooldown_time_ = std::chrono::steady_clock::now();
    edge_perception_drive_duration_ = std::chrono::seconds(0);
    // 多区域
    beacon_status_ = BeaconStatus(-1, 0);
    current_mark_id_ = -1;
    mark_detection_duration_ = std::chrono::seconds(0);
}

void NavigationRechargeAlg::SetQRCodeLocationResult(const QRCodeLocationResult &qrcode_loc_result)
{
    if (save_qr_data_)
    {
        std::lock_guard<std::mutex> lock(qr_detect_mutex_);
        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE)
        {
            qr_detect_x_.push_back(qrcode_loc_result.xyzrpw.x);
            qr_detect_y_.push_back(qrcode_loc_result.xyzrpw.y);
            qr_detect_yaw_.push_back(qrcode_loc_result.xyzrpw.w);
        }
    }
}
void NavigationRechargeAlg::SetChargeStationResult(const ChargeStationDetectResult &station_result)
{
    station_result_is_head_ = station_result.is_head;
    if (save_station_data_ && station_result.is_head)
    {
        std::lock_guard<std::mutex> lock(charge_detect_mutex_);
        head_center_error_ = station_result.head_center_error;
    }
}
void NavigationRechargeAlg::SetMCUSensor(const mower_msgs::msg::McuSensor &data)
{
    charge_terminal_status_ = data.charge_terminal_status;
    if (save_terminal_data_)
    {
        std::lock_guard<std::mutex> lock(terminal_mtx_);
        if (charge_terminal_status_)
        {
            terminal_data_.push_back(1);
        }
        else
        {
            terminal_data_.push_back(0);
        }
    }
}

void NavigationRechargeAlg::DealFeatureSelect(ThreadControl control, bool state)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData feature{control, static_cast<int>(NavAlgCtrlState::IGNORE)};

    feature.alg_status = state ? static_cast<int>(NavAlgCtrlState::ENABLE) : static_cast<int>(NavAlgCtrlState::DISABLE);

    feature_data.push_back(feature);

    if (feature_select_callback_ && !feature_data.empty())
    {
        feature_select_callback_(feature_data);
    }
}
void NavigationRechargeAlg::CrossRegionDisable()
{
    DealFeatureSelect(ThreadControl::CROSS_REGION_THREAD, false);
}

void NavigationRechargeAlg::CrossRegionEnable()
{
    DealFeatureSelect(ThreadControl::CROSS_REGION_THREAD, true);
}

void NavigationRechargeAlg::EdgeFollowDisable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, false);
}

void NavigationRechargeAlg::EdgeFollowEnable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, true);
}

void NavigationRechargeAlg::RandomMowerDisable()
{
    DealFeatureSelect(ThreadControl::RANDOM_MOWING_THREAD, false);
}

void NavigationRechargeAlg::RandomMowerEnable()
{
    DealFeatureSelect(ThreadControl::RANDOM_MOWING_THREAD, true);
}

void NavigationRechargeAlg::ProhibitVelPublisher()
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(0, 0);
        vel_publisher_->SetProhibitFlag(true);
    }
}

void NavigationRechargeAlg::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular, duration_ms);
        if (duration_ms > 0)
        {
            while (!vel_publisher_->IsExecutionCompleted())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }
}

void NavigationRechargeAlg::PublishZeroVelocity()
{
    PublishVelocity(0, 0);
}

/**
 * @brief 处理回充小车原地旋转一周过程if (qr_code_direction_is_ok_ && qr_code_y_is_ok_)
 *
 * @param charge_station_result
 * @param qr_code_result
 * @return true
 * @return false
 */
bool NavigationRechargeAlg::ProcessRechargeRotate(const ChargeStationDetectResult &charge_station_result, const QRCodeLocationResult &qr_code_result)
{
    bool ret = false;
    // 判断充电桩识别结果
    if ((charge_station_result.is_head ||
         qr_code_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_NO_POSE ||
         qr_code_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE))
    {
        if (charge_station_result.head_center_error < -head_center_min_dist_)
        {
            PublishVelocity(0, recharge_pile_angular_);
        }
        else if (charge_station_result.head_center_error > head_center_min_dist_)
        {
            PublishVelocity(0, -recharge_pile_angular_);
        }
        else
        {
            ret = true;
        }
    }
    return ret;
}

/**
 * @brief
 *
 * @param qr_x_set qr_y_set qr_yaw_set
 * @return qr_x_avg qr_y_avg qr_yaw_avg
 */
std::vector<float> NavigationRechargeAlg::Process_QRdata(std::vector<float> qr_x_set, std::vector<float> qr_y_set, std::vector<float> qr_yaw_set)
{
    std::cout << "qr_x_set:";
    for (size_t i = 0; i < qr_x_set.size(); i++)
    {
        std::cout << qr_x_set[i] << ":";
    }
    std::cout << std::endl;
    std::cout << "qr_y_set:";
    for (size_t i = 0; i < qr_y_set.size(); i++)
    {
        std::cout << qr_y_set[i] << ":";
    }
    std::cout << std::endl;
    std::cout << "qr_yaw_set:";
    for (size_t i = 0; i < qr_yaw_set.size(); i++)
    {
        std::cout << qr_yaw_set[i] << ":";
    }
    std::cout << std::endl;
    std::sort(qr_x_set.begin(), qr_x_set.end());
    std::sort(qr_y_set.begin(), qr_y_set.end());
    std::sort(qr_yaw_set.begin(), qr_yaw_set.end());
    while ((qr_x_set[qr_x_set.size() - 1] - qr_x_set[0]) > 0.3)
    {
        for (size_t i = 0; i < qr_x_set.size(); i++)
        {
            std::cout << qr_x_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_x_set.size() <= 2)
        {
            break;
        }
        qr_x_set.erase(qr_x_set.begin());
        qr_x_set.pop_back();
        std::sort(qr_x_set.begin(), qr_x_set.end());
    }
    while ((qr_y_set[qr_y_set.size() - 1] - qr_y_set[0]) > 0.3)
    {
        for (size_t i = 0; i < qr_y_set.size(); i++)
        {
            std::cout << qr_y_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_y_set.size() <= 2)
        {
            break;
        }
        qr_y_set.erase(qr_y_set.begin());
        qr_y_set.pop_back();
        std::sort(qr_y_set.begin(), qr_y_set.end());
    }
    while ((qr_yaw_set[qr_yaw_set.size() - 1] - qr_yaw_set[0]) > 0.3)
    {
        for (size_t i = 0; i < qr_yaw_set.size(); i++)
        {
            std::cout << qr_yaw_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_yaw_set.size() <= 2)
        {
            break;
        }
        qr_yaw_set.erase(qr_yaw_set.begin());
        qr_yaw_set.pop_back();
        std::sort(qr_yaw_set.begin(), qr_yaw_set.end());
    }
    std::cout << "qr_x_set:";
    for (size_t i = 0; i < qr_x_set.size(); i++)
    {
        std::cout << qr_x_set[i] << ":";
    }
    std::cout << std::endl;
    std::cout << "qr_y_set:";
    for (size_t i = 0; i < qr_y_set.size(); i++)
    {
        std::cout << qr_y_set[i] << ":";
    }
    std::cout << std::endl;
    std::cout << "qr_yaw_set:";
    for (size_t i = 0; i < qr_yaw_set.size(); i++)
    {
        std::cout << qr_yaw_set[i] << ":";
    }
    std::cout << std::endl;
    float qr_x_avg = std::accumulate(qr_x_set.begin(), qr_x_set.end(), 0.0) / qr_x_set.size();
    float qr_y_avg = std::accumulate(qr_y_set.begin(), qr_y_set.end(), 0.0) / qr_y_set.size();
    float qr_yaw_avg = std::accumulate(qr_yaw_set.begin(), qr_yaw_set.end(), 0.0) / qr_yaw_set.size();
    LOG_WARN("QRCodeData : qr_x_avg:{}, qr_y_avg:{}, qr_yaw_avg:{}.", qr_x_avg, qr_y_avg, qr_yaw_avg);
    std::vector<float> qr_data;
    qr_data.push_back(qr_x_avg);
    qr_data.push_back(qr_y_avg);
    qr_data.push_back(qr_yaw_avg);
    return qr_data;
}

/**
 * @brief
 *
 * @param qr_data_set
 * @return qr_filter_data
 */
float NavigationRechargeAlg::Fliter_QRdata(std::deque<float> &qr_data_set)
{
    if (qr_data_set.size() > 2)
    {
        qr_data_set.pop_front();
    }
    if (qr_data_set.size() == 2)
    {
        if (fabs(qr_data_set.back() - qr_data_set.front()) > 0.25)
        {
            qr_data_set.pop_back();
            return qr_data_set.front();
        }
        else
        {
            return qr_data_set.back();
        }
    }
    return qr_data_set.front();
}

/**
 * @brief 处理二维码检测结果
 *
 * @param qr_code_detect_result
 * @return true
 * @return false
 */
bool NavigationRechargeAlg::ProcessQRCodeDetect(const ChargeStationDetectResult &charge_station_detect_result,
                                                const QRCodeLocationResult &qr_code_detect_result, McuExceptionStatus &mcu_exception_status)
{
    bool ret = false;
    QRCodeDetectStatus status = qr_code_detect_result.detect_status;
    VecXYZRPW xyzrpw = qr_code_detect_result.xyzrpw;

    std::vector<float> qr_avg_data;
    switch (status)
    {
    case QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE:
        ResetOtherStates();
        if (qr_code_direction_is_ok_ && qr_code_y_is_ok_)
        {
            if (pre_timestamp_ms_ != qr_code_detect_result.timestamp_ms)
            {
                pre_timestamp_ms_ = qr_code_detect_result.timestamp_ms;
                uint64_t detect_qr_run_time = GetTimestampMs() - qr_code_detect_result.timestamp_ms;
                LOG_WARN("detect_qr_run_time: {}!", detect_qr_run_time);
                LOG_WARN("QR code 2 direction and y ok, yaw: {} y: {} x: {} timestamp_ms: {}!", xyzrpw.w, xyzrpw.y, xyzrpw.x, qr_code_detect_result.timestamp_ms);
                if ((fabs(pre_x_ - xyzrpw.x) > 1e-6) && (fabs(pre_y_ - xyzrpw.y) > 1e-6) && (fabs(pre_yaw_ - xyzrpw.w) > 1e-6))
                {
                    LOG_WARN("Filter QR code 2 direction and y ok, yaw: {} y: {} x: {} timestamp_ms: {}!", xyzrpw.w, xyzrpw.y, xyzrpw.x, qr_code_detect_result.timestamp_ms);
                    pre_x_ = xyzrpw.x;
                    pre_y_ = xyzrpw.y;
                    pre_yaw_ = xyzrpw.w;
                    float qr_filter_x, qr_filter_y, qr_filter_w;
                    if (record_stable_x_data_ == 1)
                    {
                        LOG_WARN("Stable_QRdata, stable_x_: {}, stable_y_: {}, stable_w_: {}.", stable_x_, stable_y_, stable_w_);
                        save_qr_x_tmp_.push_back(stable_x_);
                        save_qr_y_tmp_.push_back(stable_y_);
                        save_qr_w_tmp_.push_back(stable_w_);
                        record_stable_x_data_++;
                    }
                    else
                    {
                        save_qr_x_tmp_.push_back(xyzrpw.x);
                        save_qr_y_tmp_.push_back(xyzrpw.y);
                        save_qr_w_tmp_.push_back(xyzrpw.w);
                    }
                    qr_filter_x = Fliter_QRdata(save_qr_x_tmp_);
                    qr_filter_y = Fliter_QRdata(save_qr_y_tmp_);
                    qr_filter_w = Fliter_QRdata(save_qr_w_tmp_);
                    ret = ProcessQRCodeDetectHavePose(charge_station_detect_result, mcu_exception_status, qr_filter_w, qr_filter_x, qr_filter_y);
                }
            }
        }
        else
        {
            if (abs(charge_station_detect_result.head_center_error) < head_center_min_dist_)
            {
                qr_avg_data = Collect_QRdata(charge_station_detect_result);
                LOG_WARN("QRCodeDetectStatus::HAVE_QR_CODE_HAVE_POSE qr code detect!: x:{}, y:{}, yaw:{}.",
                         qr_avg_data[0], qr_avg_data[1], qr_avg_data[2]);
                if (charge_station_detect_result.is_chargestation && charge_station_detect_result.is_head)
                {
                    int charge_station_center = (charge_station_detect_result.station_box[2] + charge_station_detect_result.station_box[4]) / 2;
                    int head_center = (charge_station_detect_result.head_box[2] + charge_station_detect_result.head_box[4]) / 2;
                    if ((charge_station_center - head_center) > station_qr_direction_min_dist_)
                    {
                        qr_avg_data[1] = fabs(qr_avg_data[1]);
                        qr_avg_data[2] = -fabs(qr_avg_data[2]);
                    }
                    else if ((charge_station_center - head_center) < -station_qr_direction_min_dist_)
                    {
                        qr_avg_data[1] = -fabs(qr_avg_data[1]);
                        qr_avg_data[2] = fabs(qr_avg_data[2]);
                    }
                }
                ret = ProcessQRCodeDetectHavePose(charge_station_detect_result, mcu_exception_status, qr_avg_data[2], qr_avg_data[0], qr_avg_data[1]);
            }
            else
            {
                head_direction_flag_ = false;
            }
        }
        break;
    default:
        if (pre_perception_timestamp_ms_ != qr_code_detect_result.timestamp_ms)
        {
            pre_perception_timestamp_ms_ = qr_code_detect_result.timestamp_ms;
            ret = ProcessQRCodeDetectNoQRCode(charge_station_detect_result, mcu_exception_status);
        }
        break;
    }

    return ret;
}

/**
 * @brief 收集稳定二维码数据
 *
 * @return Stable QR data
 */
void NavigationRechargeAlg::Record_Stable_QRdata(const ChargeStationDetectResult &charge_station_detect_result)
{
    record_stable_x_data_++;
    if (record_stable_x_data_ == 1)
    {
        std::vector<float> stable_data = Collect_QRdata(charge_station_detect_result);
        stable_x_ = stable_data[0];
        stable_y_ = stable_data[1];
        stable_w_ = stable_data[2];
        LOG_WARN("Stable QRCodeData : x:{}, y:{}, yaw:{}.", stable_data[0], stable_data[1], stable_data[2]);
    }
}

bool NavigationRechargeAlg::Recharge_Terminal_Check(McuExceptionStatus &mcu_exception_status)
{
    LOG_WARN("Recharge Terminal Check And Treminal Status:{}.", charge_terminal_status_);
    bool ret = false;
    if ((!charge_terminal_status_))
    {
        if (!collision_occur_)
        {
            if ((mcu_exception_status == McuExceptionStatus::COLLISION) || (mcu_exception_status == McuExceptionStatus::LIFTING))
            {
                if (mcu_exception_status == McuExceptionStatus::LIFTING)
                {
                    LOG_ERROR("Recharge Terminal Check Lift Occur And Back!!!");
                    ResetRechargeFlags(true);                                    // 重新对桩
                    UpdateRechargeRunningState(RechargeRunningState::UNDEFINED); // 未定义 /**该状态允许切换状态
                }
                else
                {
                    LOG_ERROR("Recharge Terminal Check Collision Occur And Saty!!!");
                    PublishVelocity(0, 0, collision_drving_time_);
                    collision_occur_ = true;
                }
            }
        }
        else
        {
            LOG_ERROR("Recharge Terminal Check Collision Occur And Back!!!");
            ret = ProcessDock();
        }
    }
    else
    {
        PublishVelocity(0, 0, stay_time_);
        LOG_WARN("Recharge Terminal Check charge_terminal_status_ is true, go charge is complete!!!");
        bool charge_terminal_status = Adjust_TerminalData();
        if (charge_terminal_status)
        {
            LOG_WARN("Recharge Terminal Check Terminal Status Stay True And Charge!!!");
            PublishZeroVelocity();
            ResetRechargeFlags(false);                                   // 对桩完成
            UpdateRechargeRunningState(RechargeRunningState::UNDEFINED); // 未定义 /**该状态允许切换状态
            ResetCrossRegionFlags();
            ResetCrossRegionBeaconStatus();
            single_region_recharge_flag_ = false;
            ret = true;
        }
        else
        {
            LOG_ERROR("Recharge Terminal Check Terminal Status Stay Error And Back!!!");
            ret = ProcessDock();
        }
    }
    return ret;
}

bool NavigationRechargeAlg::Final_Recharge_Opt(McuExceptionStatus &mcu_exception_status)
{
    bool ret = false;
    if ((!charge_terminal_status_))
    {
        if (!collision_occur_)
        {
            if (straight_time_ == 0 && straight_begin_time_ == 0)
            {
                straight_time_ = straight_driving_time_; // 前进持续时间 ms
                straight_begin_time_ = GetTimestampMs();
            }
            uint64_t run_time = GetTimestampMs() - straight_begin_time_;
            LOG_WARN_THROTTLE(1500, "Straight Line Time, run time {} all_time {}!", run_time, straight_time_);
            if (run_time < straight_time_)
            {
                if ((mcu_exception_status == McuExceptionStatus::COLLISION) || (mcu_exception_status == McuExceptionStatus::LIFTING))
                {
                    if (mcu_exception_status == McuExceptionStatus::LIFTING)
                    {
                        LOG_ERROR("Lift Occur And Back!!!");
                        ResetRechargeFlags(true);                                    // 重新对桩
                        UpdateRechargeRunningState(RechargeRunningState::UNDEFINED); // 未定义 /**该状态允许切换状态
                    }
                    else
                    {
                        LOG_ERROR("Collision Occur And Saty!!!");
                        PublishVelocity(0, 0, collision_drving_time_);
                        collision_occur_ = true;
                    }
                }
                else
                {
                    PublishVelocity(recharge_pile_linear_, 0);
                }
            }
            else
            {
                LOG_ERROR("Run Time Occur And Back!!!");
                ret = ProcessDock();
            }
        }
        else
        {
            LOG_ERROR("Collision Occur And Back!!!");
            ret = ProcessDock();
        }
    }
    else
    {
        PublishVelocity(0, 0, stay_time_);
        LOG_WARN("charge_terminal_status_ is true, go charge is complete!!!");
        bool charge_terminal_status = Adjust_TerminalData();
        if (charge_terminal_status)
        {
            LOG_WARN("Terminal Status Stay True And Charge!!!");
            PublishZeroVelocity();
            ResetRechargeFlags(false);                                   // 对桩完成
            UpdateRechargeRunningState(RechargeRunningState::UNDEFINED); // 未定义 /**该状态允许切换状态
            ResetCrossRegionFlags();
            ResetCrossRegionBeaconStatus();
            single_region_recharge_flag_ = false;
            ret = true;
        }
        else
        {
            LOG_ERROR("Terminal Status Stay Error And Back!!!");
            ret = ProcessDock();
        }
    }
    return ret;
}

/**
 * @brief 收集充电桩端子数据
 *
 * @return Terminal Data
 */
bool NavigationRechargeAlg::Adjust_TerminalData()
{
    bool charge_terminal_status = true;
    save_terminal_data_ = false;
    terminal_mtx_.lock();
    terminal_data_.clear();
    terminal_mtx_.unlock();
    save_terminal_data_ = true;
    LOG_INFO("Terminal Data Start Collect.");
    TimeDiff time_diff;
    while (1)
    {
        if (time_diff.GetDiffMs() >= save_terminal_time_)
        {
            break;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    save_terminal_data_ = false;
    LOG_INFO("Terminal Data End Collect.");
    terminal_mtx_.lock();
    auto it = std::find(terminal_data_.begin(), terminal_data_.end(), 0);
    charge_terminal_status = (it != terminal_data_.end()) ? false : true;
    std::cout << "terminal_data:";
    for (size_t i = 0; i < terminal_data_.size(); i++)
    {
        std::cout << terminal_data_[i] << ":";
    }
    std::cout << std::endl;
    terminal_mtx_.unlock();
    return charge_terminal_status;
}

/**
 * @brief 收集二维码数据
 *
 * @return QR data
 */
std::vector<float> NavigationRechargeAlg::Collect_QRdata(const ChargeStationDetectResult &charge_station_detect_result)
{
    (void)charge_station_detect_result;
    PublishVelocity(0, 0);
    save_qr_data_ = false;
    qr_detect_mutex_.lock();
    qr_detect_x_.clear();
    qr_detect_y_.clear();
    qr_detect_yaw_.clear();
    qr_detect_mutex_.unlock();
    save_qr_data_ = true;

    // int record_times_ = 0;
    int try_times_ = 0;
    auto start_time = std::chrono::steady_clock::now();
    while (1)
    {
        qr_detect_mutex_.lock();
        int size = qr_detect_x_.size();
        qr_detect_mutex_.unlock();
        if (size >= save_data_num_)
        {
            break;
        }
        auto end_time = std::chrono::steady_clock::now();
        auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
        if ((uint64_t)duration_ms >= stay_all_time_)
        {
            start_time = end_time;
            uint64_t t1 = (explore_distance_ / explore_vel_) * 1000;
            PublishVelocity(explore_vel_, 0, t1);
            qr_detect_mutex_.lock();
            qr_detect_x_.clear();
            qr_detect_y_.clear();
            qr_detect_yaw_.clear();
            qr_detect_mutex_.unlock();
            try_times_++;
            LOG_INFO("Stay And Collect Data, try_times_: {}", try_times_);
        }
        if (try_times_ > try_max_num_)
        {
            LOG_INFO("Qrcode Collect Timeout!");
            if ((adjust_num_ > 0) && (station_result_is_head_))
            {
                perception_recharge_flag_ = true; // 感知上桩
            }
            else if ((adjust_num_ > 0) && (!station_result_is_head_))
            {
                rotate_recharge_flag_ = true;
            }
            else
            {
                ResetRechargeFlags(false);                                   // 重新对桩
                UpdateRechargeRunningState(RechargeRunningState::UNDEFINED); // 未定义 /**该状态允许切换状态
            }
            std::vector<float> qr_error_data{0, 0, 0};
            return qr_error_data;
        }
    }
    save_qr_data_ = false;
    qr_detect_mutex_.lock();
    std::vector<float> qr_avg_data = Process_QRdata(qr_detect_x_, qr_detect_y_, qr_detect_yaw_);
    qr_detect_mutex_.unlock();
    return qr_avg_data;
}

void NavigationRechargeAlg::OneStageAdjust(float first_turn_flag, float second_turn_flag, float qr_yaw)
{
    uint64_t t1 = (first_turn_flag * fabs(qr_yaw) / recharge_pile_angular_) * 1000; // 转弯持续时间 ms
    drvie_doing_time_ = true;
    PublishVelocity(0, second_turn_flag * recharge_pile_angular_, t1);
    PublishVelocity(0, 0, 200);
    drvie_doing_time_ = false;
    adjust_num_++;
}

void NavigationRechargeAlg::TurnAcuteAdjust(float first_turn_flag, float second_turn_flag, float third_turn_flag, float qr_x, float qr_y, float qr_yaw)
{
    (void)third_turn_flag;
    (void)first_turn_flag;
    drvie_doing_time_ = true;
    if (((qr_y > 0) && (qr_yaw > 0)) || ((qr_y < 0) && (qr_yaw < 0)))
    {
        uint64_t t1 = (M_PI - fabs(qr_yaw)) / recharge_pile_angular_ * 1000; // 转弯持续时间 ms
        PublishVelocity(0, -second_turn_flag * recharge_pile_angular_, t1);
    }
    else
    {
        uint64_t t1 = (M_PI + fabs(qr_yaw)) / recharge_pile_angular_ * 1000; // 转弯持续时间 ms
        PublishVelocity(0, -second_turn_flag * recharge_pile_angular_, t1);
    }
    float dist = qr_code_x_min_dist_ - fabs(qr_x);
    if (dist >= fabs(qr_y))
    {
        uint64_t t2 = ((dist - fabs(qr_y)) / recharge_adjust_linear_) * 1000;
        PublishVelocity(recharge_adjust_linear_, 0, t2);
        uint64_t t3 = M_PI / 2 / recharge_pile_angular_ * 1000; // 转弯持续时间 ms
        float turn_pile_linear_ = recharge_pile_angular_ * fabs(qr_y);
        PublishVelocity(turn_pile_linear_, -second_turn_flag * recharge_pile_angular_, t3);
    }
    else
    {
        uint64_t t2 = M_PI / 2 / recharge_pile_angular_ * 1000; // 转弯持续时间 ms
        float turn_pile_linear_ = recharge_pile_angular_ * dist;
        PublishVelocity(turn_pile_linear_, -second_turn_flag * recharge_pile_angular_, t2);
        uint64_t t3 = ((fabs(qr_y) - dist) / recharge_adjust_linear_) * 1000;
        PublishVelocity(recharge_adjust_linear_, 0, t3);
    }
    uint64_t t4 = M_PI / 2 / recharge_pile_angular_ * 1000; // 转弯持续时间 ms
    PublishVelocity(0, -second_turn_flag * recharge_pile_angular_, t4);
    PublishVelocity(0, 0, 200);
    drvie_doing_time_ = false;
    adjust_num_++;
}

void NavigationRechargeAlg::TurnObtuseAdjust(float first_turn_flag, float second_turn_flag, float third_turn_flag, float qr_x, float qr_y, float qr_yaw)
{
    (void)first_turn_flag;
    drvie_doing_time_ = true;
    float angle_0 = std::atan2(fabs(qr_y), (fabs(qr_x) - qr_code_x_min_dist_) / 2);
    if (((qr_y > 0) && (qr_yaw > 0)) || ((qr_y < 0) && (qr_yaw < 0)))
    {
        uint64_t t1 = (angle_0 + fabs(qr_yaw)) / recharge_pile_angular_ * 1000; // 转弯持续时间 ms
        PublishVelocity(0, second_turn_flag * recharge_pile_angular_, t1);
    }
    else
    {
        uint64_t t1 = (angle_0 - fabs(qr_yaw)) / recharge_pile_angular_ * 1000; // 转弯持续时间 ms
        PublishVelocity(0, second_turn_flag * recharge_pile_angular_, t1);
    }
    float dist = sqrt(pow(fabs(qr_y), 2) + pow((fabs(qr_x) - qr_code_x_min_dist_) / 2, 2)) - (fabs(qr_x) - qr_code_x_min_dist_) / 2;
    uint64_t t2 = (dist / recharge_adjust_linear_) * 1000;
    PublishVelocity(recharge_adjust_linear_, 0, t2);
    float circle_r = std::tan((M_PI - angle_0) / 2) * (fabs(qr_x) - qr_code_x_min_dist_) / 2;
    if (circle_r > circle_r_dist_)
    {
        float turn_pile_angular_ = recharge_adjust_linear_ / circle_r;
        uint64_t t3 = (angle_0 / turn_pile_angular_) * 1000;
        PublishVelocity(recharge_adjust_linear_, third_turn_flag * turn_pile_angular_, t3);
    }
    else
    {
        float turn_pile_linear_ = recharge_pile_angular_ * circle_r;
        uint64_t t3 = (angle_0 / recharge_pile_angular_) * 1000;
        PublishVelocity(turn_pile_linear_, third_turn_flag * recharge_pile_angular_, t3);
    }
    PublishVelocity(0, 0, 200);
    drvie_doing_time_ = false;
    adjust_num_++;
}
void NavigationRechargeAlg::AdjustYAndYawError(float first_turn_flag, float second_turn_flag, float third_turn_flag, float qr_x, float qr_y, float qr_yaw)
{
    if ((adjust_num_ == 0) && (fabs(qr_x) < qr_code_x_min_dist_) && (dock_num_ == 0))
    {
        TurnAcuteAdjust(first_turn_flag, second_turn_flag, third_turn_flag, qr_x, qr_y, qr_yaw);
    }
    else

    {
        TurnObtuseAdjust(first_turn_flag, second_turn_flag, third_turn_flag, qr_x, qr_y, qr_yaw);
    }
}

void NavigationRechargeAlg::AdjustRotate()
{
    if (!((qr_code_direction_is_ok_ && qr_code_y_is_ok_) || (adjust_num_ >= 2)))
    {
        save_station_data_ = true;
        PublishVelocity(0, 0, 2 * stay_time_);
        save_station_data_ = false;
        if (abs(head_center_error_) >= head_center_min_dist_)
        {
            head_direction_flag_ = false;
        }
    }
}

bool NavigationRechargeAlg::ProcessDock()
{
    bool ret = false;
    if (dock_num_ >= 3)
    {
        dock_success_ = false;
        qr_code_direction_is_ok_ = true;
        qr_code_y_is_ok_ = true;
        ResetCrossRegionFlags();
        ResetCrossRegionBeaconStatus();
        single_region_recharge_flag_ = false;
        LOG_ERROR("send ALG_PNC_RECHARGE_FAIL_3_TIMES_EXCEPTION!");
        PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_RECHARGE_FAIL_3_TIMES_EXCEPTION);
        ret = true;
    }
    else
    {
        ResetRechargeFlags(true);                                    // 重新对桩
        UpdateRechargeRunningState(RechargeRunningState::UNDEFINED); // 未定义 /**该状态允许切换状态
    }
    return ret;
}

/**
 * @brief 没有检测二维码检测算法EdgeFollow的情况
 */
void NavigationRechargeAlg::ProcessNoQRCodeAndEdgeFollow()
{
    // 重新充电桩检测、二维码检测过程
    if (!is_in_cooldown_time_)
    {
        CrossRegionDisable();         // 关闭跨区域进程
        RandomMowerDisable();         // 关闭随机割草
        is_qr_code_detected_ = false; // 未检测二维码检
        EdgeFollowEnable();           // 开启沿边
        ResetRechargeFlags(false);
        UpdateRechargeRunningState(RechargeRunningState::EDGE_FINDING_QR_CODE); // 未定义 /**该状态允许切换状态
    }
}

/**
 * @brief 采用感知进行上桩情况
 *
 * @param charge_station_detect_result 充电桩头检测结果
 * @return true
 * @return false
 */
bool NavigationRechargeAlg::ProcessNoQRCodeAndPerceptionRecharge(const ChargeStationDetectResult &charge_station_detect_result, McuExceptionStatus &mcu_exception_status)
{
    bool ret = false;
    if ((!charge_terminal_status_))
    {
        if (!collision_occur_)
        {
            if (perception_straight_time_ == 0 && perception_straight_begin_time_ == 0)
            {
                perception_straight_time_ = perception_straight_driving_time_; // 前进持续时间 ms
                perception_straight_begin_time_ = GetTimestampMs();
            }
            uint64_t run_time = GetTimestampMs() - perception_straight_begin_time_;
            LOG_WARN_THROTTLE(1500, "Perception Straight Line Time, run time {} all_time {}!", run_time, perception_straight_time_);
            if (run_time < perception_straight_time_)
            {
                if ((mcu_exception_status == McuExceptionStatus::COLLISION) || (mcu_exception_status == McuExceptionStatus::LIFTING))
                {
                    if (mcu_exception_status == McuExceptionStatus::LIFTING)
                    {
                        LOG_ERROR("In Perception Recharge Process, Lift Occur And Back!!!");
                        ResetRechargeFlags(true);                                    // 重新对桩
                        UpdateRechargeRunningState(RechargeRunningState::UNDEFINED); // 未定义 /**该状态允许切换状态
                    }
                    else
                    {
                        LOG_ERROR("In Perception Recharge Process, Collision Occur And Saty!!!");
                        PublishVelocity(0, 0, collision_drving_time_);
                        collision_occur_ = true;
                    }
                }
                else
                {
                    if (charge_station_detect_result.is_head)
                    {
                        float angle_vel = kp_perception_ * charge_station_detect_result.head_center_error;
                        LOG_WARN("In Perception Recharge Process,, angle_vel: {} !", angle_vel);
                        PublishVelocity(recharge_pile_linear_, angle_vel);
                    }
                    else
                    {
                        PublishVelocity(recharge_pile_linear_, 0);
                    }
                }
            }
            else
            {
                LOG_ERROR("In Perception Recharge Process, Run Time Occur And Back!!!");
                ret = ProcessDock();
            }
        }
        else
        {
            LOG_ERROR("In Perception Recharge Process, Collision Occur And Back!!!");
            ret = ProcessDock();
        }
    }
    else
    {
        PublishVelocity(0, 0, stay_time_);
        LOG_WARN("In Perception Recharge Process, charge_terminal_status_ is true, go charge is complete!!!");
        bool charge_terminal_status = Adjust_TerminalData();
        if (charge_terminal_status)
        {
            LOG_ERROR("In Perception Recharge Process, Terminal Status Stay True And Charge!!!");
            PublishZeroVelocity();
            ResetRechargeFlags(false);                                   // 对桩完成
            UpdateRechargeRunningState(RechargeRunningState::UNDEFINED); // 未定义 /**该状态允许切换状态
            ResetCrossRegionFlags();
            ResetCrossRegionBeaconStatus();
            single_region_recharge_flag_ = false;
            ret = true;
        }
        else
        {
            LOG_ERROR("In Perception Recharge Process, Terminal Status Stay Error And Back!!!");
            ret = ProcessDock();
        }
    }
    return ret;
}

/**
 * @brief 采用旋转进行上桩情况
 *
 * @param charge_station_detect_result 充电桩头检测结果
 * @return true
 * @return false
 */
bool NavigationRechargeAlg::ProcessNoQRCodeAndRotateRecharge(const ChargeStationDetectResult &station_result, const QRCodeLocationResult &qrcode_result, McuExceptionStatus &mcu_exception_status)
{
    (void)mcu_exception_status;
    bool ret = false;
    if (rotate_time_ == 0 && rotate_begin_time_ == 0)
    {
        rotate_time_ = 4 * M_PI / recharge_pile_angular_ * 1000; // 前进持续时间 ms
        rotate_begin_time_ = GetTimestampMs();
    }
    uint64_t run_time = GetTimestampMs() - rotate_begin_time_;
    if (run_time < rotate_time_)
    {
        // 如果二维码检测有结果了，且距离小于阈值，则认为靠近充电桩了
        if ((sqrt(pow(qrcode_result.xyzrpw.x, 2) + pow(qrcode_result.xyzrpw.y, 2)) < start_recharge_distance_) &&
            (abs(station_result.head_center_error) < head_center_max_dist_) &&
            fabs(qrcode_result.xyzrpw.w) < qr_code_clear_angle_ && station_result.is_head && (qrcode_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE))
        {
            LOG_WARN("In Rotate Recharge Process, QR code 2 direction and y ok, yaw: {} y: {} x: {}!", qrcode_result.xyzrpw.w, qrcode_result.xyzrpw.y, qrcode_result.xyzrpw.x);
            PublishZeroVelocity();
            ret = true;
        }
        else
        {
            LOG_WARN("In Rotate Recharge Process, Turn Left.");
            PublishVelocity(0, recharge_pile_angular_);
        }
    }
    else
    {
        LOG_ERROR("send ALG_PNC_NO_RECHARGE_STATION_EXCEPTION!");
        PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_NO_RECHARGE_STATION_EXCEPTION);
    }

    return ret;
}

/**
 * @brief pid角速度阈值限制
 *
 * @param kp PID调节量
 * @param error 控制输入偏差
 * @return angle_vel 输出角速度
 */
float NavigationRechargeAlg::LimitAngleVel(float kp, float error)
{
    float angle_vel = kp * (-error);
    if (angle_vel > recharge_max_adjust_angular_)
    {
        angle_vel = recharge_max_adjust_angular_;
    }
    if (angle_vel < -recharge_max_adjust_angular_)
    {
        angle_vel = -recharge_max_adjust_angular_;
    }
    return angle_vel;
}

/**
 * @brief 车体坐标系转换为摄像头坐标系
 *
 * @param qr_yaw qr yaw
 * @param qr_y qr y
 * @return qr_rect_y 转换后的y
 */
float NavigationRechargeAlg::RobottoCameraTF(float qr_yaw, float qr_y)
{
    float qr_rect_y = 0;
    if (qr_yaw > 0)
    {
        qr_rect_y = 0.37 * sin(qr_yaw) + qr_y;
    }
    else
    {
        qr_rect_y = -(0.37 * sin(fabs(qr_yaw)) - qr_y);
    }
    return qr_rect_y;
}

/**
 * @brief 处理二维码检测算法输出完整位姿情况
 *
 * @param qr_yaw 二维码检测结果 yaw 角
 * @param qr_x 二维码检测结果 x 方向轴距离
 * @param qr_y 二维码检测结果 y 方向轴距离
 * @param angle
 * @return true
 * @return false
 */
bool NavigationRechargeAlg::ProcessQRCodeDetectHavePose(const ChargeStationDetectResult &charge_station_detect_result, McuExceptionStatus &mcu_exception_status, float qr_yaw, float qr_x, float qr_y)
{
    bool ret = false;
    no_qr_num_ = 0;
    if (!(qr_code_direction_is_ok_ && qr_code_y_is_ok_))
    {
        if (adjust_num_ >= 2)
        {
            qr_code_direction_is_ok_ = true;
            qr_code_y_is_ok_ = true;
            Record_Stable_QRdata(charge_station_detect_result);
        }
    }

    // 航向角和Y方向均调整到小于设置阈值，认为小车已对齐充电桩
    if (qr_code_direction_is_ok_ && qr_code_y_is_ok_)
    {
        adjust_num_ = 2;
        float qr_rect_y = RobottoCameraTF(qr_yaw, qr_y);
        if (fabs(qr_x) > qr_code_min_distance_)
        {
            LOG_WARN("QR code 2 direction and y ok, yaw: {} y: {} x: {} min_dis: {}!", qr_yaw, qr_rect_y, qr_x, qr_code_min_distance_);
            LOG_WARN("Detect Box left_x: {} left_y: {} right_x: {} right_y: {}!", charge_station_detect_result.head_box[2],
                     charge_station_detect_result.head_box[3], charge_station_detect_result.head_box[4], charge_station_detect_result.head_box[5]);
            if ((std::abs(qr_rect_y) > 0.01))
            {
                LOG_WARN("qr_y: {}!", qr_rect_y);
                LOG_WARN("qr_y_pid_out: {}", kp_y_ * (-qr_rect_y));
                float angle_vel = LimitAngleVel(kp_y_, qr_rect_y);
                PublishVelocity(recharge_pile_linear_, angle_vel);
            }
            else
            {
                if (fabs(qr_yaw) > 0.02)
                {
                    LOG_WARN("qr_yaw: {}!", qr_yaw);
                    LOG_WARN("qr_yaw_pid_out: {}", kp_yaw_ * (-qr_yaw));
                    float angle_vel = LimitAngleVel(kp_yaw_, qr_yaw);
                    PublishVelocity(recharge_pile_linear_, angle_vel);
                }
                else
                {
                    LOG_WARN("Stage Start Straight Line Drving, qr_y:{}, qr_yaw: {}!", qr_y, qr_yaw);
                    PublishVelocity(recharge_pile_linear_, 0);
                }
            }
            ret = Recharge_Terminal_Check(mcu_exception_status);
        }
        else
        {
            qr_code_x_is_ok_ = true;
            ret = Final_Recharge_Opt(mcu_exception_status);
        }
    }
    else
    {
        float y_dis = fabs(qr_y);
        if (y_dis >= qr_code_y_min_dist_)
        {
            if (qr_yaw <= 0)
            {
                if (qr_y < 0)
                {
                    AdjustYAndYawError(1.0, 1.0, -1.0, qr_x, qr_y, qr_yaw);
                }
                else
                {
                    AdjustYAndYawError(-1.0, -1.0, 1.0, qr_x, qr_y, qr_yaw);
                }
            }
            else
            {
                if (qr_y < 0)
                {
                    AdjustYAndYawError(-1.0, 1.0, -1.0, qr_x, qr_y, qr_yaw);
                }
                else
                {
                    AdjustYAndYawError(1.0, -1.0, 1.0, qr_x, qr_y, qr_yaw);
                }
            }
        }
        else // yaw_avg > 0
        {
            qr_code_y_is_ok_ = true;
            if (fabs(qr_yaw) >= qr_code_detect_angle_1_)
            {
                if (qr_yaw < 0)
                {
                    OneStageAdjust(1.0, 1.0, qr_yaw);
                }
                else
                {
                    OneStageAdjust(1.0, -1.0, qr_yaw);
                }
            }
            else
            {
                LOG_DEBUG("QR code  direction OK and y OK.");
                qr_code_direction_is_ok_ = true;
                Record_Stable_QRdata(charge_station_detect_result);
            }
        }
    }
    AdjustRotate();
    return ret;
}

/**
 * @brief 处理二维码检测无法检测到二维码的情况
 *
 */
bool NavigationRechargeAlg::ProcessQRCodeDetectNoQRCode(const ChargeStationDetectResult &charge_station_detect_result, McuExceptionStatus &mcu_exception_status)
{
    bool ret = false;
    // 待办：没有检测到二维码的处理逻辑
    LOG_DEBUG("ERROR, QR code detect No Pose, no qr code detect! {} {} {}", qr_code_direction_is_ok_ ? 1 : 0,
              qr_code_y_is_ok_ ? 1 : 0, is_move_to_station_complete_ ? 1 : 0);
    if (!(perception_recharge_flag_ || rotate_recharge_flag_))
    {
        if ((adjust_num_ > 0) && (!drvie_doing_time_))
        {
            no_qr_num_++;
            if (qr_code_direction_is_ok_ && qr_code_y_is_ok_ && (!qr_code_x_is_ok_))
            {
                if (charge_station_detect_result.is_head)
                {
                    float angle_vel = kp_perception_ * charge_station_detect_result.head_center_error;
                    LOG_WARN("In No QR Code Recharge Process,, angle_vel: {} !", angle_vel);
                    PublishVelocity(recharge_pile_linear_, angle_vel);
                }
                else
                {
                    LOG_WARN("In No QR Code And Straight Drving!");
                    PublishVelocity(recharge_pile_linear_, 0);
                }
                ret = Recharge_Terminal_Check(mcu_exception_status);
            }
            else if (qr_code_direction_is_ok_ && qr_code_y_is_ok_ && qr_code_x_is_ok_)
            {
                LOG_WARN("Final Straight Drving In Recharge Process!");
                ret = Final_Recharge_Opt(mcu_exception_status);
            }
            else
            {
                LOG_INFO("Process QRdata And No QRdata, no_qr_num_: {}", no_qr_num_);
                if (no_qr_num_ > no_qr_max_num_)
                {
                    bool charge_head_flag = false;
                    if (charge_station_detect_result.is_head)
                    {
                        no_qr_charge_station_head_data_.push_back(1);
                    }
                    else
                    {
                        no_qr_charge_station_head_data_.push_back(0);
                    }

                    if ((int)no_qr_charge_station_head_data_.size() > no_qr_save_head_detect_num_)
                    {
                        for (size_t i = 0; i < no_qr_charge_station_head_data_.size(); i++)
                        {
                            if (no_qr_charge_station_head_data_[i] == 1)
                            {
                                charge_head_flag = true;
                            }
                        }
                        no_qr_charge_station_head_data_.clear();
                        if (charge_head_flag)
                        {
                            LOG_WARN("No QR Code And Running Perception Recharge");
                            perception_recharge_flag_ = true; // 感知上桩
                            ResetOtherStates();
                        }
                        else
                        {
                            LOG_WARN("No QR Code And Running Rotate Recharge");
                            rotate_recharge_flag_ = true;
                            ResetOtherStates();
                        }
                    }
                }
            }
        }
        else
        {
            no_qr_num_++;
            if ((!drvie_doing_time_) && (no_qr_num_ > 1))
            {
                LOG_INFO("No QRdata And No QR NUM: {}.", no_qr_num_);
                PublishVelocity(0, recharge_pile_angular_, rotate_edge_following_time_);
                ProcessNoQRCodeAndEdgeFollow();
            }
        }
    }
    return ret;
}

void NavigationRechargeAlg::PauseVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->PauseVelocity();
    }
}

void NavigationRechargeAlg::ResumeVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->ResumeVelocity();
    }
}

void NavigationRechargeAlg::UpdateRechargeRunningState(RechargeRunningState state)
{
    if (recharge_running_state_callback_)
    {
        recharge_running_state_callback_(state);
    }
}

void NavigationRechargeAlg::SetRechargeRunningStateCallback(std::function<void(RechargeRunningState)> callback)
{
    recharge_running_state_callback_ = callback;
}

const char *NavigationRechargeAlg::GetVersion()
{
    return "V1.0.0";
}

void NavigationRechargeAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_exception_ = std::make_unique<iox_exception_publisher>(
        iox::capro::ServiceDescription{kSocExceptionIox[0],
                                       kSocExceptionIox[1],
                                       kSocExceptionIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
}

void NavigationRechargeAlg::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_recharge_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception)
            .or_else([](auto &error) {
                LOG_ERROR("Navigation recharge publish soc exception Unable to publishCopyOf, error: {}", error);
            });
    }
}

} // namespace fescue_iox