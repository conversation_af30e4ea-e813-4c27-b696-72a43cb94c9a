#pragma once

#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

struct NavigationRechargeAlgConfig
{
    int try_max_num{2};                    // 重复探索最大次数
    int no_qr_max_num{30};                 // 记录得不到QR码最大次数
    int save_data_num{5};                  // 保存数据帧数
    int save_terminal_num{10};             // 保存数据帧数
    int head_center_min_dist{45};          // 充电桩头像素偏差
    int head_center_max_dist{275};         // 回充充电桩可调像素距离阈值
    int station_qr_direction_min_dist{30}; // 二维码检测中心和充电桩检测中心相对方位最小像素阈值

    float qr_code_clear_angle{1.13};      // 1.13(65度)
    float qr_code_detect_angle_1{0.1745}; // 0.2618(15度), 0.1745(10度)，0.0873(5度)
    float qr_code_min_distance{0.55};     // 离二维码最近距离 0.65 m
    float start_recharge_distance{1.75};  // 开始回充距离 1.5 m
    float qr_code_x_min_dist{0.97};       // 调整y和yaw时x的最大距离阈值 0.92 m
    float qr_code_y_min_dist{0.07};
    float circle_r_dist{1.0}; // 转弯半径最大距离阈值 1.2 m
    float explore_distance{0.075};
    float explore_vel{0.075};
    float kp_y{3.0};                    // PID变量kp_y
    float kp_yaw{3.0};                  // PID变量kp_yaw
    float kp_perception{-0.004};        // PID变量kp_perception
    float recharge_adjust_linear{0.25}; // 回充充电桩调整线速度
    float recharge_pile_linear{0.1};    // 上桩初始阶段线速度
    float recharge_pile_angular{0.25};  // 上桩角速度

    NavigationRechargeAlgConfig() = default;
    ~NavigationRechargeAlgConfig() = default;
    NavigationRechargeAlgConfig(const NavigationRechargeAlgConfig &config) = default;
    NavigationRechargeAlgConfig &operator=(const NavigationRechargeAlgConfig &config);
    std::string toString() const;
};

bool operator==(const NavigationRechargeAlgConfig &lhs, const NavigationRechargeAlgConfig &rhs);
bool operator!=(const NavigationRechargeAlgConfig &lhs, const NavigationRechargeAlgConfig &rhs);

} // namespace fescue_iox
