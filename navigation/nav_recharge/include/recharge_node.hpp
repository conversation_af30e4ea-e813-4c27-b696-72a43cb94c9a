#ifndef NAVIGATION_RECHARGE_NODE_HPP
#define NAVIGATION_RECHARGE_NODE_HPP

#include "data_type.hpp"
#include "geometry_msgs/twist__struct.h"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/server.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/charge_pile_dock_status.hpp"
#include "mower_msgs/msg/mcu_exception.hpp"
#include "mower_msgs/msg/mcu_mission_info.hpp"
#include "mower_msgs/msg/mcu_sensor.hpp"
#include "ob_mower_msgs/charge_result_struct.h"
#include "ob_mower_msgs/mark_location_result_struct.h"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/nav_cross_region_final_result__struct.h"
#include "ob_mower_msgs/nav_cross_region_state__struct.h"
#include "ob_mower_msgs/nav_recharge_final_result__struct.h"
#include "ob_mower_msgs/nav_recharge_state__struct.h"
#include "ob_mower_msgs/nav_running_state__struct.h"
#include "ob_mower_msgs/qrcode_result__struct.h"
#include "ob_mower_srvs/nav_recharge_node_param_service__struct.h"
#include "recharge.hpp"
#include "recharge_config.hpp"
#include "utils/file.hpp"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_client_mower.hpp"
#include "utils/iceoryx_publisher_mower.hpp"
#include "utils/iceoryx_server_mower.hpp"
#include "utils/iceoryx_subscriber_mower.hpp"
#include "utils/thread_safe_queue.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <mutex>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

class NavigationRechargeNode
{
    using iox_twist_publisher = iox::popo::Publisher<geometry_msgs__msg__Twist_iox>;
    using iox_cross_region_state_publisher = iox::popo::Publisher<fescue_msgs__msg__CrossRegionStateData>;
    using iox_nav_alg_ctrl_publisher = iox::popo::Publisher<fescue_msgs__msg__NavigationAlgoCtrlData>;
    using iox_nav_recharge_result_publisher = iox::popo::Publisher<fescue_msgs__msg__NavRechargeFinalResult>;
    using iox_recharge_state_publisher = iox::popo::Publisher<fescue_msgs__msg__RechargeStateData>;

    using get_node_param_request = fescue_msgs__srv__GetNavigationRechargeNodeParam_Request;
    using get_node_param_response = fescue_msgs__srv__GetNavigationRechargeNodeParam_Response;
    using set_node_param_request = fescue_msgs__srv__SetNavigationRechargeNodeParam_Request;
    using set_node_param_response = fescue_msgs__srv__SetNavigationRechargeNodeParam_Response;

public:
    NavigationRechargeNode(const std::string &node_name);
    ~NavigationRechargeNode();

private:
    void InitWorkingDirectory();
    void InitParam();
    void InitAlgorithmParam();
    void ConfigParamToAlgorithmParam(const NavigationRechargeAlgConfig &config, RechargeAlgParam &param);
    void InitLogger();
    void InitPublisher();
    void InitSubscriber();
    void InitAlgorithm();
    void InitService();
    void DeinitAlgorithm();
    void CheckMCUExeceptionTimeout();
    void InitHeartbeat();

private:
    void DealMCUSensor(const mower_msgs::msg::McuSensor &data);
    void DealMcuMissionInfo(const mower_msgs::msg::McuMissionInfo &data);
    void DealMCUException(const mower_msgs::msg::McuException &data);
    void DealChargeDetectResult(const fescue_msgs__msg__ChargeResult &msg);
    void DealQRCodeLocationResult(const fescue_msgs__msg__QrCodeResult &msg);

    void DealMarkLocationResult(const fescue_msgs__msg__MarkLocationResult &msg);
    void DealCrossRegionState(const fescue_msgs__msg__CrossRegionStateData &msg);
    void PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result);

    void DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg);
    void DealChargePileDockStatus(const mower_msgs::msg::ChargePileDockStatus &msg);
    void DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data);
    void DealCrossRegionRunningStateCallback(CrossRegionRunningState state);
    void RechargeThread();
    void ResetSubData();
    void DealFeatureSelectCallback(const std::vector<FeatureSelectData> &data);
    bool GetRechargeNodeParam(fescue_msgs__msg__NavigationRechargeNodeParam &data);
    bool SetRechargeNodeParam(const fescue_msgs__msg__NavigationRechargeNodeParam &data);
    void PublishRechargeFinalResult(const RechargeAlgResult &result);
    void SetRechargeVelPublisherProhibit(bool prohibit)
    {
        if (recharge_alg_)
        {
            recharge_alg_->SetVelPublisherProhibit(prohibit);
        }
    }

    void DealRechargeRunningStateCallback(RechargeRunningState state);

private:
    // subscriber
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__MarkLocationResult>> sub_mark_loc_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__CrossRegionStateData>> sub_cross_region_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__ChargeResult>> sub_charge_detect_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::ChargePileDockStatus>> sub_charge_pile_dock_status_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__QrCodeResult>> sub_qrcode_loc_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>> sub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>> sub_nav_running_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuSensor>> sub_mcu_sensor_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuException>> sub_mcu_exception_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuMissionInfo>> sub_mcu_mission_info_{nullptr};

    // publisher
    std::unique_ptr<iox_cross_region_state_publisher> pub_cross_region_state_{nullptr};
    std::unique_ptr<iox_nav_alg_ctrl_publisher> pub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<iox_nav_recharge_result_publisher> pub_nav_recharge_final_result_{nullptr};
    std::unique_ptr<iox_recharge_state_publisher> pub_recharge_state_{nullptr};

    // service
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};

private:
    bool is_in_mow_{false};
    std::string node_name_{"navigation_recharge_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string recharge_alg_conf_file_{"conf/navigation_recharge_node/recharge.yaml"};

    std::thread recharge_thread_;
    std::atomic_bool thread_running_{true};
    std::atomic_bool recharge_enable_{false};

    std::mutex station_mtx_;
    std::mutex qrcode_loc_mtx_;
    std::mutex mcu_exception_mutex_;
    std::mutex mark_loc_mtx_;
    std::mutex cross_region_mtx_;

    MarkLocationResult mark_loc_result_;
    CrossRegionRunningState cross_region_state_{CrossRegionRunningState::UNDEFINED};
    ThreadSafeQueue<CrossRegionRunningState> cross_region_running_state_queue_{64};
    CrossRegionRunningState last_cross_region_running_state_{CrossRegionRunningState::UNDEFINED};
    McuExceptionStatus mcu_exception_status_{McuExceptionStatus::NORMAL};
    ChargeStationDetectResult charge_station_result_;
    QRCodeLocationResult qrcode_loc_result_;

    // 新增
    std::chrono::steady_clock::time_point last_mcu_exception_time_;

    RechargeAlgParam recharge_alg_param_;
    std::unique_ptr<NavigationRechargeAlg> recharge_alg_{nullptr};

    std::unique_ptr<FileWriter> writer_{nullptr};
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
};

} // namespace fescue_iox

#endif
