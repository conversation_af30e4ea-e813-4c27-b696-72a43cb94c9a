#include "imu_data_processor.hpp"

#include "utils/time.hpp"

#include <algorithm>
#include <cmath>
#include <fstream>
#include <iomanip>
#include <sstream>
#include <thread>

namespace fescue_iox
{

ImuDataProcessor::ImuDataProcessor(const ImuProcessorParam &param)
    : param_(param)
    , last_imu_time_(std::chrono::steady_clock::now())
{
    LOG_INFO("[ImuDataProcessor] Initialize IMU data processor");
    bias_samples_.reserve(param_.bias_calibration_samples);
}

ImuDataProcessor::~ImuDataProcessor()
{
    Shutdown();
    LOG_INFO("[ImuDataProcessor] IMU data processor shutdown");
}

void ImuDataProcessor::Initialize()
{
    LOG_INFO("[ImuDataProcessor] Initialize IMU data processing system");

    // 初始化数据日志
    if (param_.enable_data_logging)
    {
        InitializeDataLogging();
    }

    processing_active_.store(true);
    LOG_INFO("[ImuDataProcessor] IMU data processor initialized successfully");
}

void ImuDataProcessor::Shutdown()
{
    LOG_INFO("[ImuDataProcessor] Shutting down IMU data processor");

    // 停止旋转控制
    StopRotationControl();

    // 停止处理
    processing_active_.store(false);

    // 关闭数据日志
    CloseDataLogging();
}

void ImuDataProcessor::SetImuData(const ImuData &imu_data)
{
    if (!processing_active_.load())
    {
        return;
    }

    std::lock_guard<std::mutex> lock(imu_data_mutex_);
    latest_imu_data_ = imu_data;
    imu_data_valid_ = true;

    // 处理IMU数据
    ProcessImuData(imu_data);
}

void ImuDataProcessor::SetImuDataCallback(std::function<void(const ImuData &)> callback)
{
    imu_data_callback_ = callback;
}

void ImuDataProcessor::SetVelocityCallback(std::function<void(float, float, uint64_t)> callback)
{
    velocity_callback_ = callback;
}

void ImuDataProcessor::ProcessImuData(const ImuData &imu_data)
{
    // 计算时间步长
    auto current_time = std::chrono::steady_clock::now();
    float dt = 0.0f;

    if (!is_first_imu_)
    {
        dt = std::chrono::duration<float>(current_time - last_imu_time_).count();
    }
    else
    {
        is_first_imu_ = false;
        LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Ignore first IMU data");
        last_imu_time_ = current_time;
        last_imu_timestamp_ = imu_data.system_timestamp;
        return;
    }

    last_imu_time_ = current_time;
    last_imu_timestamp_ = imu_data.system_timestamp;

    // IMU零偏校准
    if (!is_bias_calibrated_)
    {
        CalibrateImuBias(imu_data);
        return;
    }

    // 应用零偏校正
    float angular_velocity_z = imu_data.angular_velocity_z - bias_z_;

    // 保存原始数据用于日志
    float raw_angular_velocity = angular_velocity_z;

    // 初始化滤波器（仅在首次使用时）
    if (!filter_initialized_)
    {
        InitializeFilters(angular_velocity_z);
        filter_initialized_ = true;
    }

    // 应用低通滤波
    angular_velocity_z = ApplyLowPassFilter(angular_velocity_z, filtered_angular_velocity_, param_.filter_alpha);

    // 记录滤波前后数据
    if (param_.enable_data_logging)
    {
        LogFilteringData(imu_data.system_timestamp, raw_angular_velocity, angular_velocity_z);
    }

    // 应用阈值滤波
    if (std::abs(angular_velocity_z) < param_.angular_velocity_threshold)
    {
        angular_velocity_z = 0.0f;
    }

    // 更新当前航向角
    current_yaw_ += angular_velocity_z * dt;

    // 更新旋转控制
    if (rotation_control_active_.load())
    {
        UpdateRotationControl(angular_velocity_z, dt);
    }

    // 更新线性运动控制
    if (linear_motion_control_active_.load())
    {
        UpdateLinearMotionControl(angular_velocity_z, dt);
    }

    // 创建处理后的IMU数据并通过回调传递
    if (imu_data_callback_)
    {
        ImuData processed_data = imu_data;
        processed_data.angular_velocity_z = angular_velocity_z;
        imu_data_callback_(processed_data);
    }
}

void ImuDataProcessor::CalibrateImuBias(const ImuData &imu_data)
{
    if (bias_samples_.size() < param_.bias_calibration_samples)
    {
        bias_samples_.push_back(imu_data.angular_velocity_z);
        LOG_INFO("[ImuDataProcessor] Collecting bias sample: {}/{}",
                 bias_samples_.size(), param_.bias_calibration_samples);
        return;
    }

    // 计算平均零偏
    float sum = 0.0f;
    for (const auto &sample : bias_samples_)
    {
        sum += sample;
    }
    bias_z_ = sum / bias_samples_.size();
    is_bias_calibrated_ = true;
    bias_samples_.clear();

    LOG_INFO("[ImuDataProcessor] Bias calibration completed, bias_z = {:.6f} rad/s", bias_z_);
}

float ImuDataProcessor::ApplyLowPassFilter(float new_value, float &filtered_value, float alpha)
{
    // 一阶低通滤波器: filtered_value = alpha * new_value + (1 - alpha) * filtered_value
    // alpha越小，滤波越强（更平滑但响应更慢）
    filtered_value = alpha * new_value + (1.0f - alpha) * filtered_value;
    return filtered_value;
}

void ImuDataProcessor::InitializeFilters(float initial_angular_velocity)
{
    filtered_angular_velocity_ = initial_angular_velocity;
    LOG_INFO("[ImuDataProcessor] Filter initialized: angular_velocity={:.4f}", initial_angular_velocity);
}

RotationControlResult ImuDataProcessor::StartRotationControl(float target_angle, float angular_velocity)
{
    std::lock_guard<std::mutex> lock(rotation_control_mutex_);

    if (rotation_control_active_.load())
    {
        LOG_WARN("[ImuDataProcessor] Rotation control already active");
        return rotation_result_;
    }

    // 初始化旋转控制状态
    target_rotation_angle_ = target_angle;
    target_angular_velocity_ = angular_velocity;
    accumulated_rotation_ = 0.0f;

    rotation_start_time_ = std::chrono::steady_clock::now();
    current_rotation_start_time_ = rotation_start_time_;

    rotation_result_ = RotationControlResult{};
    rotation_result_.target_rotation = target_angle;

    // 初始化后退状态
    backup_attempt_count_ = 0;
    is_backing_up_ = false;

    rotation_control_active_.store(true);

    // 启动旋转控制线程
    if (rotation_control_thread_.joinable())
    {
        rotation_control_thread_.join();
    }
    rotation_control_thread_ = std::thread(&ImuDataProcessor::RotationControlThread, this);

    LOG_INFO("[ImuDataProcessor] Started rotation control: target={:.3f}°, velocity={:.3f}°/s, velocity={:.3f}rad/s",
             target_angle * 180.0f / M_PI, angular_velocity * 180.0f / M_PI, angular_velocity);

    return rotation_result_;
}

void ImuDataProcessor::StopRotationControl()
{
    if (rotation_control_active_.load())
    {
        rotation_control_active_.store(false);

        if (rotation_control_thread_.joinable())
        {
            rotation_control_thread_.join();
        }

        LOG_INFO("[ImuDataProcessor] Rotation control stopped");
    }
}

bool ImuDataProcessor::IsRotationControlActive() const
{
    return rotation_control_active_.load();
}

LinearMotionControlResult ImuDataProcessor::StartLinearMotionControl(float target_distance, float linear_velocity, float target_heading)
{
    if (!is_bias_calibrated_)
    {
        LOG_ERROR("[ImuDataProcessor] Cannot start linear motion control: IMU bias not calibrated");
        LinearMotionControlResult result;
        result.completed = false;
        result.timeout = true;
        return result;
    }

    // 停止之前的线性运动控制
    StopLinearMotionControl();

    // 初始化线性运动控制状态
    {
        std::lock_guard<std::mutex> lock(linear_motion_control_mutex_);
        target_distance_ = target_distance;
        target_heading_ = target_heading;
        target_linear_velocity_ = linear_velocity;
        accumulated_distance_ = 0.0f;
        initial_heading_ = current_yaw_;

        linear_motion_result_ = LinearMotionControlResult{};
        linear_motion_result_.target_distance = target_distance;
        linear_motion_result_.completed = false;
        linear_motion_result_.timeout = false;
    }

    linear_motion_start_time_ = std::chrono::steady_clock::now();
    linear_motion_control_active_.store(true);

    // 启动线性运动控制线程
    if (linear_motion_control_thread_.joinable())
    {
        linear_motion_control_thread_.join();
    }
    linear_motion_control_thread_ = std::thread(&ImuDataProcessor::LinearMotionControlThread, this);

    LOG_INFO("[ImuDataProcessor] Started linear motion control: target_distance={:.3f}m, velocity={:.3f}m/s, target_heading={:.1f}°",
             target_distance, linear_velocity, target_heading * 180.0f / M_PI);

    return linear_motion_result_;
}

void ImuDataProcessor::StopLinearMotionControl()
{
    if (linear_motion_control_active_.load())
    {
        linear_motion_control_active_.store(false);

        if (linear_motion_control_thread_.joinable())
        {
            linear_motion_control_thread_.join();
        }

        LOG_INFO("[ImuDataProcessor] Linear motion control stopped");
    }
}

bool ImuDataProcessor::IsLinearMotionControlActive() const
{
    return linear_motion_control_active_.load();
}

void ImuDataProcessor::RotationControlThread()
{
    LOG_INFO("[ImuDataProcessor] Rotation control thread started");

    while (rotation_control_active_.load())
    {
        LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Rotation control thread running");

        auto current_time = std::chrono::steady_clock::now();
        float total_elapsed_time = std::chrono::duration<float>(current_time - rotation_start_time_).count();

        {
            std::lock_guard<std::mutex> lock(rotation_control_mutex_);
            rotation_result_.elapsed_time = total_elapsed_time;

            // 如果正在后退
            if (is_backing_up_)
            {
                float backup_elapsed = std::chrono::duration<float>(current_time - backup_start_time_).count();
                float backup_duration = param_.backup_distance / param_.backup_speed;

                if (backup_elapsed >= backup_duration)
                {
                    // 后退完成，停止后退，重新开始旋转
                    is_backing_up_ = false;
                    current_rotation_start_time_ = current_time;

                    // 停止后退运动
                    if (velocity_callback_)
                    {
                        velocity_callback_(0.0f, 0.0f, 100); // 停止100ms
                    }

                    LOG_INFO("[ImuDataProcessor] Backup completed, resuming rotation. Attempt: {}/{}",
                             backup_attempt_count_, param_.max_backup_attempts);
                }
                else
                {
                    // 继续后退
                    if (velocity_callback_)
                    {
                        velocity_callback_(-param_.backup_speed, 0.0f, 0); // 持续后退
                    }
                }
            }
            else
            {
                // 正常旋转模式
                float current_rotation_elapsed = std::chrono::duration<float>(current_time - current_rotation_start_time_).count();

                // 检查当前旋转是否超时
                if (current_rotation_elapsed >= param_.max_rotation_time)
                {
                    // 旋转超时，检查是否还能后退
                    if (backup_attempt_count_ < param_.max_backup_attempts)
                    {
                        // 开始后退
                        backup_attempt_count_++;
                        is_backing_up_ = true;
                        backup_start_time_ = current_time;

                        LOG_WARN("[ImuDataProcessor] Rotation timeout after {:.1f}s, starting backup attempt {}/{}",
                                 current_rotation_elapsed, backup_attempt_count_, param_.max_backup_attempts);

                        // 开始后退运动
                        if (velocity_callback_)
                        {
                            velocity_callback_(-param_.backup_speed, 0.0f, 0); // 开始后退
                        }
                    }
                    else
                    {
                        // 已达到最大后退次数，旋转失败
                        rotation_result_.timeout = true;
                        rotation_result_.completed = false;
                        LOG_ERROR("[ImuDataProcessor] Rotation failed after {} backup attempts, total time: {:.1f}s",
                                  param_.max_backup_attempts, total_elapsed_time);
                        break;
                    }
                }
                else
                {
                    // 检查旋转完成
                    float rotation_error = std::abs(target_rotation_angle_ - accumulated_rotation_);
                    rotation_result_.rotation_error = rotation_error;
                    rotation_result_.actual_rotation = accumulated_rotation_;

                    LOG_INFO_THROTTLE(500, "[ImuDataProcessor] Rotation status: target={:.3f}°, actual={:.3f}°, error={:.3f}°",
                                      target_rotation_angle_ * 180.0f / M_PI,
                                      accumulated_rotation_ * 180.0f / M_PI,
                                      rotation_error * 180.0f / M_PI);

                    if (rotation_error <= param_.rotation_tolerance)
                    {
                        rotation_result_.completed = true;
                        rotation_result_.timeout = false;
                        LOG_INFO("[ImuDataProcessor] Rotation completed: target={:.3f}°, actual={:.3f}°, error={:.3f}°",
                                 target_rotation_angle_ * 180.0f / M_PI,
                                 accumulated_rotation_ * 180.0f / M_PI,
                                 rotation_error * 180.0f / M_PI);
                        break;
                    }
                    else
                    {
                        // 继续旋转 - 发布旋转速度命令
                        float sign = target_rotation_angle_ >= 0.0f ? 1.0f : -1.0f;
                        if (velocity_callback_)
                        {
                            velocity_callback_(0.0f, sign * target_angular_velocity_, 0); // 持续旋转
                        }
                    }
                }
            }
        }

        // 控制频率 (50Hz)
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

    // 确保停止所有运动
    if (velocity_callback_)
    {
        velocity_callback_(0.0f, 0.0f, 100);
    }

    rotation_control_active_.store(false);
    LOG_INFO("[ImuDataProcessor] Rotation control thread exited");
}

void ImuDataProcessor::LinearMotionControlThread()
{
    LOG_INFO("[ImuDataProcessor] Linear motion control thread started");

    const float distance_tolerance = 0.05f; // 5cm tolerance
    const float heading_tolerance = 0.087f; // 5 degrees tolerance
    const float max_motion_time = 60.0f;    // 60 seconds max
    const float correction_gain = 0.3f;     // Angular velocity correction gain
    const float max_angular_vel = 0.3f;     // Maximum angular velocity for correction

    while (linear_motion_control_active_.load())
    {
        LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Linear motion control thread running");

        auto current_time = std::chrono::steady_clock::now();
        float total_elapsed_time = std::chrono::duration<float>(current_time - linear_motion_start_time_).count();

        {
            std::lock_guard<std::mutex> lock(linear_motion_control_mutex_);
            linear_motion_result_.elapsed_time = total_elapsed_time;

            // 检查超时
            if (total_elapsed_time >= max_motion_time)
            {
                linear_motion_result_.timeout = true;
                linear_motion_result_.completed = false;
                LOG_ERROR("[ImuDataProcessor] Linear motion control timeout after {:.1f}s", total_elapsed_time);
                break;
            }

            // 计算距离误差
            float distance_error = std::abs(target_distance_ - accumulated_distance_);
            linear_motion_result_.distance_error = distance_error;
            linear_motion_result_.actual_distance = accumulated_distance_;

            // 计算航向误差
            float current_heading = current_yaw_;
            float heading_error = target_heading_ - current_heading;

            // 归一化航向误差到 [-π, π]
            while (heading_error > M_PI)
                heading_error -= 2 * M_PI;
            while (heading_error < -M_PI)
                heading_error += 2 * M_PI;

            linear_motion_result_.heading_error = heading_error;

            LOG_INFO_THROTTLE(500, "[ImuDataProcessor] Linear motion status: target_dist={:.3f}m, actual_dist={:.3f}m, dist_error={:.3f}m, heading_error={:.1f}°",
                              target_distance_, accumulated_distance_, distance_error, heading_error * 180.0f / M_PI);

            // 检查距离完成
            if (distance_error <= distance_tolerance)
            {
                linear_motion_result_.completed = true;
                linear_motion_result_.timeout = false;
                LOG_INFO("[ImuDataProcessor] Linear motion completed: target_dist={:.3f}m, actual_dist={:.3f}m, error={:.3f}m",
                         target_distance_, accumulated_distance_, distance_error);
                break;
            }
            else
            {
                // 继续运动 - 计算速度命令
                float linear_velocity = target_linear_velocity_;
                float angular_velocity = 0.0f;

                // 如果需要航向纠正
                if (std::abs(heading_error) > heading_tolerance)
                {
                    angular_velocity = heading_error * correction_gain;
                    angular_velocity = std::max(-max_angular_vel, std::min(max_angular_vel, angular_velocity));
                }

                // 发布速度命令
                if (velocity_callback_)
                {
                    velocity_callback_(linear_velocity, angular_velocity, 0); // 持续运动
                }
            }
        }

        // 控制频率 (50Hz)
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

    // 确保停止所有运动
    if (velocity_callback_)
    {
        velocity_callback_(0.0f, 0.0f, 100);
    }

    linear_motion_control_active_.store(false);
    LOG_INFO("[ImuDataProcessor] Linear motion control thread exited");
}

void ImuDataProcessor::UpdateRotationControl(float angular_velocity, float dt)
{
    (void)angular_velocity; // 标记参数为已使用，避免编译警告

    if (!rotation_control_active_.load())
    {
        return;
    }

    std::lock_guard<std::mutex> lock(rotation_control_mutex_);

    // 累积旋转角度
    accumulated_rotation_ += angular_velocity * dt;

    LOG_INFO_THROTTLE(10, "[ImuDataProcessor][UpdateRotationControl1] Rotation update: angular_velocity={:.3f}rad/s", angular_velocity);

    LOG_INFO_THROTTLE(10, "[ImuDataProcessor][UpdateRotationControl1] Rotation update: accumulated={:.3f}°, target={:.3f}°",
                      accumulated_rotation_ * 180.0f / M_PI,
                      target_rotation_angle_ * 180.0f / M_PI);
}

void ImuDataProcessor::UpdateLinearMotionControl(float angular_velocity, float dt)
{
    (void)angular_velocity; // 标记参数为已使用，避免编译警告

    if (!linear_motion_control_active_.load())
    {
        return;
    }

    std::lock_guard<std::mutex> lock(linear_motion_control_mutex_);

    // 估算移动距离（简化方法，假设主要是直线运动）
    // 在实际应用中，可能需要更复杂的里程计算法
    float distance_increment = target_linear_velocity_ * dt;
    accumulated_distance_ += distance_increment;

    LOG_INFO_THROTTLE(10, "[ImuDataProcessor][UpdateLinearMotionControl] Distance update: accumulated={:.3f}m, target={:.3f}m",
                      accumulated_distance_, target_distance_);
}

bool ImuDataProcessor::IsBiasCalibrated() const
{
    return is_bias_calibrated_;
}

float ImuDataProcessor::getCurrentYaw() const
{
    return current_yaw_;
}

float ImuDataProcessor::getBiasZ() const
{
    return bias_z_;
}

void ImuDataProcessor::SetParam(const ImuProcessorParam &param)
{
    param_ = param;
}

ImuProcessorParam ImuDataProcessor::GetParam() const
{
    return param_;
}

void ImuDataProcessor::ResetState()
{
    std::lock_guard<std::mutex> lock(imu_data_mutex_);

    // 重置校准状态
    // is_bias_calibrated_ = false;
    // filter_initialized_ = false;
    // bias_z_ = 0.0f;
    // bias_samples_.clear();
    // bias_samples_.reserve(param_.bias_calibration_samples);

    // 重置滤波状态
    // filtered_angular_velocity_ = 0.0f;

    // 重置时间状态
    // is_first_imu_ = true;
    last_imu_timestamp_ = 0;
    last_imu_time_ = std::chrono::steady_clock::now();

    // 重置当前状态
    current_yaw_ = 0.0f;

    // 停止旋转控制
    StopRotationControl();

    LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] State reset completed");
}

void ImuDataProcessor::InitializeDataLogging()
{
    try
    {
        log_file_.open(param_.log_file_path, std::ios::out | std::ios::trunc);
        if (log_file_.is_open())
        {
            // 写入CSV头部
            log_file_ << "timestamp,raw_angular_velocity,filtered_angular_velocity\n";
            logging_initialized_ = true;
            LOG_INFO("[ImuDataProcessor] Data logging initialized: {}", param_.log_file_path);
        }
        else
        {
            LOG_ERROR("[ImuDataProcessor] Failed to open log file: {}", param_.log_file_path);
        }
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("[ImuDataProcessor] Exception in data logging initialization: {}", e.what());
    }
}

void ImuDataProcessor::LogFilteringData(uint64_t timestamp, float raw_angular_velocity, float filtered_angular_velocity)
{
    if (!logging_initialized_ || !log_file_.is_open())
    {
        return;
    }

    try
    {
        log_file_ << timestamp << ","
                  << std::fixed << std::setprecision(6)
                  << raw_angular_velocity << ","
                  << filtered_angular_velocity << "\n";
        log_file_.flush();
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("[ImuDataProcessor] Exception in data logging: {}", e.what());
    }
}

void ImuDataProcessor::CloseDataLogging()
{
    if (logging_initialized_ && log_file_.is_open())
    {
        log_file_.close();
        logging_initialized_ = false;
        LOG_INFO("[ImuDataProcessor] Data logging closed");
    }
}

} // namespace fescue_iox
