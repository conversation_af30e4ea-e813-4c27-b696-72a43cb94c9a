#pragma once

#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

struct NavigationCrossRegionAlgConfig
{
    // 多区域通道参数
    float cross_region_linear{0.15};            /*param*/
    float cross_region_angular{0.5};            /*param*/
    float max_distance_threshold{0.95};         /*param*/
    float min_distance_threshold{0.65};         /*param*/
    float cross_region_special_linear{0.15};    // Linear velocity in special cases (e.g., perception-driven) /*param*/
    float cross_region_special_angular{0.2};    // Angular velocity in special cases (e.g., perception-driven) /*param*/
    float dis_tolerance{0.0};                   // Distance threshold for adjustment to prevent redundant rotation /*param*/
    float cross_region_angle_compensation{0.0}; // Angle compensation for cross-region /*param*/

    float channel_stop_pose_x{-0.5};               /*param*/
    int grass_count_threshold{7};                  /*param*/
    int edge_mode_direction{-1};                   // Default counterclockwise -1 /*param*/
    float channel_width{1.5};                      // Channel width /*param*/
    float camera_2_center_dis{0.37};               // Distance from the car's camera to the rotation center is 0.45 /*param*/
    float adjust_mode_x_direction_threshold{-0.3}; // X-direction threshold in adjustment mode before crossing the channel, default -0.5 /*param*/

    float mark_distance_threshold{0.5};              // 1.5 Beacon distance threshold relative to the car's camera, used to determine if within the region /*param*/
    int perception_drive_cooldown_time_threshold{3}; // 20s Perception-driven cooldown time /*param*/

    float cross_region_adjust_displace{0.7}; // 0.7 Adjustment displacement after crossing the region /*param*/
    float channel_fixed_distance{0.2};       // 3.0 Fixed channel passing distance for FindBeaconsPhase_41_New /*param*/
    NavigationCrossRegionAlgConfig() = default;
    ~NavigationCrossRegionAlgConfig() = default;
    NavigationCrossRegionAlgConfig(const NavigationCrossRegionAlgConfig &config) = default;
    NavigationCrossRegionAlgConfig &operator=(const NavigationCrossRegionAlgConfig &config);
    std::string toString() const;
};

bool operator==(const NavigationCrossRegionAlgConfig &lhs, const NavigationCrossRegionAlgConfig &rhs);
bool operator!=(const NavigationCrossRegionAlgConfig &lhs, const NavigationCrossRegionAlgConfig &rhs);

} // namespace fescue_iox
