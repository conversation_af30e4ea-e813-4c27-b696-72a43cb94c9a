#ifndef STUCK_DETECTION_RECOVERY_HPP
#define STUCK_DETECTION_RECOVERY_HPP

#include "data_type.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "sliding_window.hpp"
#include "utils/logger.hpp"
#include "velocity_publisher.hpp"

#include <atomic>
#include <chrono>
#include <deque>
#include <fstream>
#include <functional>
#include <memory>
#include <mutex>
#include <thread>
#include <vector>

namespace fescue_iox
{

// Enumeration of stuck recovery modes
enum class RecoveryMode
{
    NONE = 0,           // No recovery
    ROTATE_LEFT,        // Rotate left
    ROTATE_RIGHT,       // Rotate right
    FORWARD,            // Move forward
    BACKWARD,           // Move backward
    SINGLE_WHEEL_LEFT,  // Rotate left wheel independently
    SINGLE_WHEEL_RIGHT, // Rotate right wheel independently
    ALTERNATING_PUSH    // Alternating push
};

// Time window detection result
struct WindowDetectionResult
{
    bool is_stuck;               // Whether stuck
    float total_rotation;        // Total rotation
    uint64_t window_duration_ms; // Window duration (ms)
    bool data_insufficient;      // Whether data is insufficient

    WindowDetectionResult()
        : is_stuck(false)
        , total_rotation(0.0f)
        , window_duration_ms(0)
        , data_insufficient(false)
    {
    }
};

// Stuck recovery parameters
struct StuckRecoveryParam
{
    // Detection parameters - Sliding window detection based on accumulated angle (5 minutes, 10 minutes, 15 minutes)
    float rotation_threshold_5min = 2.09f;  // Minimum rotation threshold for 5-minute window (rad) - approximately 120 degrees
    float rotation_threshold_10min = 4.19f; // Minimum rotation threshold for 10-minute window (rad) - approximately 240 degrees
    float rotation_threshold_15min = 6.28f; // Minimum rotation threshold for 15-minute window (rad) - approximately 360 degrees
    int min_stuck_windows = 1;              // Minimum number of windows that must detect a stuck condition to be considered stuck

    // Recovery parameters
    float initial_linear_speed = 0.5f;  // Initial linear speed (m/s)
    float initial_angular_speed = 0.6f; // Initial angular speed (rad/s)
    float max_linear_speed = 1.0f;      // Maximum linear speed (m/s)
    float max_angular_speed = 1.2f;     // Maximum angular speed (rad/s)
    float speed_increment = 0.1f;       // Speed increment

    // Recovery action duration
    uint64_t recovery_action_duration_ms = 2000; // Duration of each recovery action (ms) - 2 seconds
    uint64_t max_recovery_duration_ms = 60000;   // Maximum recovery duration (ms) - 2 minutes （old 5 minutes）

    // Vehicle parameters
    float wheel_radius = 0.1f; // Wheel radius (m)
    float wheel_base = 0.335f; // Wheel base (m)

    // Data logging
    bool enable_data_logging = false;                                    // Whether to enable data logging
    std::string log_file_path = "/userdata/log/stuck_recovery_data.csv"; // Log file path
};

class StuckDetectionRecovery
{
public:
    explicit StuckDetectionRecovery(const StuckRecoveryParam &param);
    ~StuckDetectionRecovery();

    // Initialization and cleanup
    void Initialize();
    void Shutdown();

    // Dynamically start/stop detection
    void StartDetection();
    void StopDetection();
    bool IsDetectionActive() const;

    // Data input interface
    void SetImuData(const ImuData &imu_data);
    void SetMotorSpeedData(const MotorSpeedData &motor_speed_data);

    // Main function interface
    bool IsStuck();                // Check if stuck
    bool StartRecovery();          // Start stuck recovery
    void StopRecovery();           // Stop stuck recovery
    bool IsRecoveryActive() const; // Check if recovery is active

    // Status management after successful recovery
    void ResetAllStates();                    // Reset all states and variables
    bool IsInRecoverySuccessCooldown() const; // Check if in recovery success cooldown period

    // Set callback function
    void SetVelocityPublisher(std::shared_ptr<VelocityPublisher> vel_publisher);
    void SetExceptionPublisher(std::function<void(mower_msgs::msg::SocExceptionLevel, mower_msgs::msg::SocExceptionValue)> exception_publisher);

    // Parameter settings
    void SetParam(const StuckRecoveryParam &param);
    StuckRecoveryParam GetParam() const;

    // Status query
    RecoveryMode GetCurrentRecoveryMode() const;
    std::vector<WindowDetectionResult> GetDetectionResults() const;

private:
    // Core detection and recovery threads
    void DetectionThread();
    void RecoveryThread();

    // Motion data processing
    void ProcessImuData(const ImuData &imu_data);
    void ProcessMotorData(const MotorSpeedData &motor_speed_data);
    void UpdateMovementData();

    // Sliding window detection
    WindowDetectionResult CheckWindow(uint64_t window_duration_ms, float min_rotation);
    bool IsStuckInMultipleWindows();

    // Sliding window management
    void InitializeSlidingWindows();
    void UpdateSlidingWindows(const MovementData &new_data);
    void ResetSlidingWindows();

    // General sliding window management (retained for compatibility)
    void CleanupMovementHistory(uint64_t max_retention_time_ms);
    std::deque<MovementData> GetWindowData(uint64_t window_duration_ms, uint64_t current_time);

    // Recovery strategy
    void ExecuteRecoveryAction(RecoveryMode mode, float linear_speed, float angular_speed);
    RecoveryMode GetNextRecoveryMode();
    void ProgressiveSpeedAdjustment();

    // Real-time motion monitoring
    bool HasMovementDuringRecovery();
    void ResetMovementTracking();

    // Data logging
    void InitializeDataLogging();
    void LogData(const MovementData &data);
    void LogFilteringData(uint64_t timestamp, float raw_angular_vel, float filtered_angular_vel);
    void LogWindowDetectionResults(const std::vector<WindowDetectionResult> &results, bool overall_stuck);
    void CloseDataLogging();

    // Utility functions
    uint64_t GetCurrentTimestamp() const;
    float CalculateRotation(float angular_vel, float dt);
    void PublishVelocity(float linear, float angular, uint64_t duration_ms = 0);
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);

    // Filtering functions
    float ApplyLowPassFilter(float new_value, float &filtered_value, float alpha);
    void InitializeFilters(float initial_angular_velocity);

private:
    // Parameters
    StuckRecoveryParam param_;

    // Thread control
    std::atomic_bool detection_running_{false};
    std::atomic_bool recovery_running_{false};
    std::atomic_bool detection_active_{false}; // Controls whether detection is active
    std::thread detection_thread_;
    std::thread recovery_thread_;

    // Data mutex locks
    std::mutex imu_mutex_;
    std::mutex motor_mutex_;
    std::mutex movement_mutex_;

    // Sensor data
    ImuData latest_imu_data_;
    MotorSpeedData latest_motor_data_;
    bool imu_data_valid_{false};
    bool motor_data_valid_{false};

    // Motion data queue (sliding window)
    std::deque<MovementData> movement_history_;
    MovementData current_movement_;

    // Independent sliding window management
    std::vector<SlidingWindow> sliding_windows_;
    std::mutex sliding_windows_mutex_;

    // Detection status
    std::atomic_bool is_stuck_{false};
    std::vector<WindowDetectionResult> detection_results_;

    // Recovery status
    std::atomic_bool recovery_active_{false};
    RecoveryMode current_recovery_mode_{RecoveryMode::NONE};
    float current_linear_speed_;
    float current_angular_speed_;
    uint64_t recovery_start_time_;
    uint64_t last_action_time_;
    int recovery_cycle_count_;

    // Real-time motion monitoring
    MovementData recovery_start_position_;
    float recovery_rotation_threshold_{1.57f}; // Minimum rotation threshold during recovery - 45 degrees

    // Velocity publisher
    std::shared_ptr<VelocityPublisher> vel_publisher_;

    // Exception publisher callback
    std::function<void(mower_msgs::msg::SocExceptionLevel, mower_msgs::msg::SocExceptionValue)> exception_publisher_;

    // Data logging
    std::ofstream data_log_file_;
    std::ofstream filter_log_file_;
    std::ofstream window_detection_log_file_;
    bool data_logging_initialized_{false};

    // IMU processing related
    bool is_first_imu_{true};
    std::chrono::steady_clock::time_point last_imu_time_;
    uint64_t last_imu_timestamp_{0};
    bool is_bias_calibrated_{false};
    float bias_z_{0.0f}; // Angular velocity bias

    float angular_velocity_z_threshold_{0.02f}; // Angular velocity threshold

    std::vector<float> calibration_samples_;        // Angular velocity calibration samples
    static constexpr int CALIBRATION_SAMPLES = 300; // 1 second of data for calibration

    // Low-pass filter related
    float filter_alpha_{0.1f};              // Filter coefficient (0-1, smaller value means stronger filtering)
    float filtered_angular_velocity_{0.0f}; // Filtered angular velocity
    bool filter_initialized_{false};        // Whether the filter is initialized

    // Recovery success cooldown management
    uint64_t recovery_success_time_{0};                                     // Timestamp of successful recovery
    static constexpr uint64_t RECOVERY_SUCCESS_COOLDOWN_MS = 1 * 60 * 1000; // 1-minute cooldown period
};

} // namespace fescue_iox

#endif // STUCK_DETECTION_RECOVERY_HPP
